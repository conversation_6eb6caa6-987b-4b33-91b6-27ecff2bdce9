// 性能优化工具类
const performanceUtils = {
  // 页面性能监控
  pagePerformance: {},
  
  // 开始性能监控
  startPerformanceMonitor(pageName) {
    const startTime = Date.now()
    this.pagePerformance[pageName] = {
      startTime: startTime,
      loadTime: null,
      renderTime: null,
      interactions: []
    }
    
    console.log(`[性能监控] ${pageName} 页面开始加载`)
  },

  // 结束性能监控
  endPerformanceMonitor(pageName) {
    if (this.pagePerformance[pageName]) {
      const endTime = Date.now()
      const loadTime = endTime - this.pagePerformance[pageName].startTime
      this.pagePerformance[pageName].loadTime = loadTime
      
      console.log(`[性能监控] ${pageName} 页面加载完成，耗时: ${loadTime}ms`)
      
      // 如果加载时间超过3秒，记录为慢页面
      if (loadTime > 3000) {
        console.warn(`[性能警告] ${pageName} 页面加载较慢: ${loadTime}ms`)
        this.reportSlowPage(pageName, loadTime)
      }
    }
  },

  // 记录用户交互
  recordInteraction(pageName, action, target) {
    if (this.pagePerformance[pageName]) {
      const interaction = {
        action: action,
        target: target,
        timestamp: Date.now(),
        responseTime: null
      }
      this.pagePerformance[pageName].interactions.push(interaction)
    }
  },

  // 记录交互响应时间
  recordInteractionResponse(pageName, responseTime) {
    if (this.pagePerformance[pageName]) {
      const interactions = this.pagePerformance[pageName].interactions
      if (interactions.length > 0) {
        const lastInteraction = interactions[interactions.length - 1]
        lastInteraction.responseTime = responseTime
        
        // 如果响应时间超过500ms，记录为慢交互
        if (responseTime > 500) {
          console.warn(`[交互警告] ${pageName} 交互响应较慢: ${responseTime}ms`)
        }
      }
    }
  },

  // 获取性能报告
  getPerformanceReport() {
    const report = {
      totalPages: Object.keys(this.pagePerformance).length,
      slowPages: [],
      slowInteractions: [],
      averageLoadTime: 0
    }

    let totalLoadTime = 0
    let validPages = 0

    Object.keys(this.pagePerformance).forEach(pageName => {
      const pageData = this.pagePerformance[pageName]
      
      if (pageData.loadTime) {
        totalLoadTime += pageData.loadTime
        validPages++
        
        // 记录慢页面
        if (pageData.loadTime > 3000) {
          report.slowPages.push({
            page: pageName,
            loadTime: pageData.loadTime
          })
        }
      }

      // 记录慢交互
      pageData.interactions.forEach(interaction => {
        if (interaction.responseTime && interaction.responseTime > 500) {
          report.slowInteractions.push({
            page: pageName,
            action: interaction.action,
            responseTime: interaction.responseTime
          })
        }
      })
    })

    if (validPages > 0) {
      report.averageLoadTime = Math.round(totalLoadTime / validPages)
    }

    return report
  },

  // 上报慢页面
  reportSlowPage(pageName, loadTime) {
    try {
      // 在实际项目中，这里可以上报到监控服务
      const reportData = {
        type: 'slow_page',
        page: pageName,
        loadTime: loadTime,
        timestamp: new Date().toISOString(),
        userAgent: wx.getSystemInfoSync()
      }
      
      // 暂时存储到本地，实际项目中应该上报到服务器
      const slowPages = wx.getStorageSync('slowPages') || []
      slowPages.push(reportData)
      
      // 只保留最近50条记录
      if (slowPages.length > 50) {
        slowPages.splice(0, slowPages.length - 50)
      }
      
      wx.setStorageSync('slowPages', slowPages)
    } catch (error) {
      console.error('上报慢页面失败:', error)
    }
  },

  // 清理性能数据
  clearPerformanceData() {
    this.pagePerformance = {}
    wx.removeStorageSync('slowPages')
  }
}

// 错误处理工具类
const errorUtils = {
  // 错误类型
  ERROR_TYPES: {
    NETWORK: 'network',
    DATA: 'data',
    UI: 'ui',
    PERMISSION: 'permission',
    UNKNOWN: 'unknown'
  },

  // 记录错误
  logError(error, context = {}) {
    const errorInfo = {
      message: error.message || error,
      stack: error.stack || '',
      type: this.getErrorType(error),
      context: context,
      timestamp: new Date().toISOString(),
      page: getCurrentPages().pop()?.route || 'unknown',
      systemInfo: wx.getSystemInfoSync()
    }

    console.error('[错误记录]', errorInfo)

    // 存储错误信息
    this.storeError(errorInfo)

    // 根据错误类型显示用户友好的提示
    this.showUserFriendlyError(errorInfo)
  },

  // 判断错误类型
  getErrorType(error) {
    const message = error.message || error.toString()
    
    if (message.includes('network') || message.includes('request')) {
      return this.ERROR_TYPES.NETWORK
    } else if (message.includes('data') || message.includes('parse')) {
      return this.ERROR_TYPES.DATA
    } else if (message.includes('permission') || message.includes('auth')) {
      return this.ERROR_TYPES.PERMISSION
    } else if (message.includes('component') || message.includes('render')) {
      return this.ERROR_TYPES.UI
    } else {
      return this.ERROR_TYPES.UNKNOWN
    }
  },

  // 存储错误信息
  storeError(errorInfo) {
    try {
      const errors = wx.getStorageSync('errorLogs') || []
      errors.push(errorInfo)
      
      // 只保留最近100条错误记录
      if (errors.length > 100) {
        errors.splice(0, errors.length - 100)
      }
      
      wx.setStorageSync('errorLogs', errors)
    } catch (e) {
      console.error('存储错误信息失败:', e)
    }
  },

  // 显示用户友好的错误提示
  showUserFriendlyError(errorInfo) {
    let title = '操作失败'
    let content = '请稍后重试'

    switch (errorInfo.type) {
      case this.ERROR_TYPES.NETWORK:
        title = '网络错误'
        content = '请检查网络连接后重试'
        break
      case this.ERROR_TYPES.DATA:
        title = '数据错误'
        content = '数据加载失败，请刷新页面'
        break
      case this.ERROR_TYPES.PERMISSION:
        title = '权限不足'
        content = '请检查相关权限设置'
        break
      case this.ERROR_TYPES.UI:
        title = '页面错误'
        content = '页面显示异常，请重新进入'
        break
    }

    wx.showToast({
      title: content,
      icon: 'none',
      duration: 2000
    })
  },

  // 获取错误统计
  getErrorStats() {
    try {
      const errors = wx.getStorageSync('errorLogs') || []
      const stats = {
        total: errors.length,
        byType: {},
        byPage: {},
        recent: errors.slice(-10)
      }

      errors.forEach(error => {
        // 按类型统计
        stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
        
        // 按页面统计
        stats.byPage[error.page] = (stats.byPage[error.page] || 0) + 1
      })

      return stats
    } catch (error) {
      console.error('获取错误统计失败:', error)
      return { total: 0, byType: {}, byPage: {}, recent: [] }
    }
  },

  // 清理错误日志
  clearErrorLogs() {
    wx.removeStorageSync('errorLogs')
  }
}

// 缓存管理工具类
const cacheUtils = {
  // 缓存配置
  CACHE_CONFIG: {
    DEFAULT_EXPIRE: 30 * 60 * 1000, // 30分钟
    MAX_CACHE_SIZE: 50, // 最大缓存条目数
    CACHE_KEY_PREFIX: 'cache_'
  },

  // 设置缓存
  setCache(key, data, expireTime = null) {
    try {
      const cacheKey = this.CACHE_CONFIG.CACHE_KEY_PREFIX + key
      const cacheData = {
        data: data,
        timestamp: Date.now(),
        expireTime: expireTime || (Date.now() + this.CACHE_CONFIG.DEFAULT_EXPIRE)
      }
      
      wx.setStorageSync(cacheKey, cacheData)
      this.cleanExpiredCache()
      return true
    } catch (error) {
      console.error('设置缓存失败:', error)
      return false
    }
  },

  // 获取缓存
  getCache(key) {
    try {
      const cacheKey = this.CACHE_CONFIG.CACHE_KEY_PREFIX + key
      const cacheData = wx.getStorageSync(cacheKey)
      
      if (!cacheData) {
        return null
      }

      // 检查是否过期
      if (Date.now() > cacheData.expireTime) {
        wx.removeStorageSync(cacheKey)
        return null
      }

      return cacheData.data
    } catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  },

  // 删除缓存
  removeCache(key) {
    try {
      const cacheKey = this.CACHE_CONFIG.CACHE_KEY_PREFIX + key
      wx.removeStorageSync(cacheKey)
      return true
    } catch (error) {
      console.error('删除缓存失败:', error)
      return false
    }
  },

  // 清理过期缓存
  cleanExpiredCache() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const now = Date.now()
      
      storageInfo.keys.forEach(key => {
        if (key.startsWith(this.CACHE_CONFIG.CACHE_KEY_PREFIX)) {
          const cacheData = wx.getStorageSync(key)
          if (cacheData && now > cacheData.expireTime) {
            wx.removeStorageSync(key)
          }
        }
      })
    } catch (error) {
      console.error('清理过期缓存失败:', error)
    }
  },

  // 清空所有缓存
  clearAllCache() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      storageInfo.keys.forEach(key => {
        if (key.startsWith(this.CACHE_CONFIG.CACHE_KEY_PREFIX)) {
          wx.removeStorageSync(key)
        }
      })
      return true
    } catch (error) {
      console.error('清空缓存失败:', error)
      return false
    }
  },

  // 获取缓存统计
  getCacheStats() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      let cacheCount = 0
      let totalSize = 0

      storageInfo.keys.forEach(key => {
        if (key.startsWith(this.CACHE_CONFIG.CACHE_KEY_PREFIX)) {
          cacheCount++
          // 估算大小（实际大小可能不准确）
          const data = wx.getStorageSync(key)
          totalSize += JSON.stringify(data).length
        }
      })

      return {
        count: cacheCount,
        estimatedSize: totalSize,
        maxSize: this.CACHE_CONFIG.MAX_CACHE_SIZE
      }
    } catch (error) {
      console.error('获取缓存统计失败:', error)
      return { count: 0, estimatedSize: 0, maxSize: 0 }
    }
  }
}

module.exports = {
  performanceUtils,
  errorUtils,
  cacheUtils
}
