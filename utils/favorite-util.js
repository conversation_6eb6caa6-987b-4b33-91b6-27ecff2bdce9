// 收藏和历史记录工具类
const favoriteUtils = {
  // 收藏类型
  TYPES: {
    MAJOR: 'major',
    FACULTY: 'faculty',
    NEWS: 'news'
  },

  // 获取收藏列表
  getFavorites(type = null) {
    try {
      const favorites = wx.getStorageSync('favorites') || {}
      if (type) {
        return favorites[type] || []
      }
      return favorites
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      return type ? [] : {}
    }
  },

  // 添加收藏
  addFavorite(type, item) {
    try {
      const favorites = this.getFavorites()
      if (!favorites[type]) {
        favorites[type] = []
      }

      // 检查是否已收藏
      const existIndex = favorites[type].findIndex(fav => fav.id === item.id)
      if (existIndex === -1) {
        const favoriteItem = {
          ...item,
          favoriteTime: new Date().toISOString(),
          type: type
        }
        favorites[type].unshift(favoriteItem)
        wx.setStorageSync('favorites', favorites)
        
        wx.showToast({
          title: '收藏成功',
          icon: 'success',
          duration: 1500
        })
        return true
      } else {
        wx.showToast({
          title: '已经收藏过了',
          icon: 'none',
          duration: 1500
        })
        return false
      }
    } catch (error) {
      console.error('添加收藏失败:', error)
      wx.showToast({
        title: '收藏失败',
        icon: 'none',
        duration: 1500
      })
      return false
    }
  },

  // 移除收藏
  removeFavorite(type, itemId) {
    try {
      const favorites = this.getFavorites()
      if (favorites[type]) {
        favorites[type] = favorites[type].filter(item => item.id !== itemId)
        wx.setStorageSync('favorites', favorites)
        
        wx.showToast({
          title: '取消收藏',
          icon: 'success',
          duration: 1500
        })
        return true
      }
      return false
    } catch (error) {
      console.error('移除收藏失败:', error)
      return false
    }
  },

  // 检查是否已收藏
  isFavorited(type, itemId) {
    try {
      const favorites = this.getFavorites(type)
      return favorites.some(item => item.id === itemId)
    } catch (error) {
      console.error('检查收藏状态失败:', error)
      return false
    }
  },

  // 切换收藏状态
  toggleFavorite(type, item) {
    if (this.isFavorited(type, item.id)) {
      return this.removeFavorite(type, item.id)
    } else {
      return this.addFavorite(type, item)
    }
  },

  // 获取收藏统计
  getFavoriteStats() {
    try {
      const favorites = this.getFavorites()
      const stats = {}
      Object.keys(this.TYPES).forEach(key => {
        const type = this.TYPES[key]
        stats[type] = (favorites[type] || []).length
      })
      stats.total = Object.values(stats).reduce((sum, count) => sum + count, 0)
      return stats
    } catch (error) {
      console.error('获取收藏统计失败:', error)
      return { total: 0 }
    }
  },

  // 清空收藏
  clearFavorites(type = null) {
    try {
      if (type) {
        const favorites = this.getFavorites()
        favorites[type] = []
        wx.setStorageSync('favorites', favorites)
      } else {
        wx.removeStorageSync('favorites')
      }
      return true
    } catch (error) {
      console.error('清空收藏失败:', error)
      return false
    }
  }
}

// 历史记录工具类
const historyUtils = {
  // 历史记录类型
  TYPES: {
    MAJOR_VIEW: 'major_view',
    FACULTY_VIEW: 'faculty_view',
    SEARCH: 'search',
    PAGE_VISIT: 'page_visit'
  },

  // 最大历史记录数量
  MAX_HISTORY: 100,

  // 获取历史记录
  getHistory(type = null) {
    try {
      const history = wx.getStorageSync('history') || {}
      if (type) {
        return history[type] || []
      }
      return history
    } catch (error) {
      console.error('获取历史记录失败:', error)
      return type ? [] : {}
    }
  },

  // 添加历史记录
  addHistory(type, item) {
    try {
      const history = this.getHistory()
      if (!history[type]) {
        history[type] = []
      }

      // 移除已存在的相同记录
      history[type] = history[type].filter(h => h.id !== item.id)

      // 添加新记录到开头
      const historyItem = {
        ...item,
        visitTime: new Date().toISOString(),
        type: type
      }
      history[type].unshift(historyItem)

      // 限制历史记录数量
      if (history[type].length > this.MAX_HISTORY) {
        history[type] = history[type].slice(0, this.MAX_HISTORY)
      }

      wx.setStorageSync('history', history)
      return true
    } catch (error) {
      console.error('添加历史记录失败:', error)
      return false
    }
  },

  // 移除历史记录
  removeHistory(type, itemId) {
    try {
      const history = this.getHistory()
      if (history[type]) {
        history[type] = history[type].filter(item => item.id !== itemId)
        wx.setStorageSync('history', history)
        return true
      }
      return false
    } catch (error) {
      console.error('移除历史记录失败:', error)
      return false
    }
  },

  // 清空历史记录
  clearHistory(type = null) {
    try {
      if (type) {
        const history = this.getHistory()
        history[type] = []
        wx.setStorageSync('history', history)
      } else {
        wx.removeStorageSync('history')
      }
      return true
    } catch (error) {
      console.error('清空历史记录失败:', error)
      return false
    }
  },

  // 获取最近访问的记录
  getRecentHistory(type, limit = 10) {
    try {
      const history = this.getHistory(type)
      return history.slice(0, limit)
    } catch (error) {
      console.error('获取最近历史记录失败:', error)
      return []
    }
  },

  // 获取历史记录统计
  getHistoryStats() {
    try {
      const history = this.getHistory()
      const stats = {}
      Object.keys(this.TYPES).forEach(key => {
        const type = this.TYPES[key]
        stats[type] = (history[type] || []).length
      })
      stats.total = Object.values(stats).reduce((sum, count) => sum + count, 0)
      return stats
    } catch (error) {
      console.error('获取历史统计失败:', error)
      return { total: 0 }
    }
  },

  // 记录页面访问
  recordPageVisit(pageName, pageData = {}) {
    const visitData = {
      id: `${pageName}_${Date.now()}`,
      pageName: pageName,
      title: pageData.title || pageName,
      ...pageData
    }
    return this.addHistory(this.TYPES.PAGE_VISIT, visitData)
  },

  // 记录搜索
  recordSearch(keyword, resultCount = 0) {
    const searchData = {
      id: `search_${Date.now()}`,
      keyword: keyword,
      resultCount: resultCount
    }
    return this.addHistory(this.TYPES.SEARCH, searchData)
  }
}

// 用户行为分析工具
const analyticsUtils = {
  // 记录用户行为
  trackEvent(eventName, eventData = {}) {
    try {
      const events = wx.getStorageSync('userEvents') || []
      const event = {
        eventName: eventName,
        eventData: eventData,
        timestamp: new Date().toISOString(),
        page: getCurrentPages().pop()?.route || 'unknown'
      }
      
      events.push(event)
      
      // 限制事件数量，保留最近1000条
      if (events.length > 1000) {
        events.splice(0, events.length - 1000)
      }
      
      wx.setStorageSync('userEvents', events)
      
      // 在实际项目中，这里可以上报到分析服务
      console.log('用户行为:', event)
    } catch (error) {
      console.error('记录用户行为失败:', error)
    }
  },

  // 获取用户行为统计
  getEventStats(days = 7) {
    try {
      const events = wx.getStorageSync('userEvents') || []
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)
      
      const recentEvents = events.filter(event => 
        new Date(event.timestamp) >= cutoffDate
      )
      
      const stats = {}
      recentEvents.forEach(event => {
        stats[event.eventName] = (stats[event.eventName] || 0) + 1
      })
      
      return {
        totalEvents: recentEvents.length,
        eventCounts: stats,
        period: `${days}天`
      }
    } catch (error) {
      console.error('获取行为统计失败:', error)
      return { totalEvents: 0, eventCounts: {}, period: `${days}天` }
    }
  },

  // 清理过期事件
  cleanupEvents(days = 30) {
    try {
      const events = wx.getStorageSync('userEvents') || []
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)
      
      const validEvents = events.filter(event => 
        new Date(event.timestamp) >= cutoffDate
      )
      
      wx.setStorageSync('userEvents', validEvents)
      return true
    } catch (error) {
      console.error('清理事件失败:', error)
      return false
    }
  }
}

module.exports = {
  favoriteUtils,
  historyUtils,
  analyticsUtils
}
