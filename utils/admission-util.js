// 招生小程序工具函数
const { generateId, getTodayString } = require('./util')

// 专业数据管理
const majorUtils = {
  // 获取所有专业
  getAllMajors() {
    return wx.getStorageSync('majors') || this.getDefaultMajors()
  },

  // 获取默认专业数据
  getDefaultMajors() {
    return [
      {
        id: 1,
        name: '机械设计制造及其自动化',
        college: 'mechanical',
        collegeName: '机电工程学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: true,
        description: '培养具备机械设计制造基础知识与应用能力的高级工程技术人才',
        employment: '95%',
        salary: '8000-12000元',
        tags: ['国家特色专业', '工程认证', '就业热门'],
        courses: {
          core: ['机械制图', '理论力学', '材料力学', '机械原理', '机械设计'],
          practical: ['金工实习', '机械设计课程设计', '专业实习'],
          elective: ['先进制造技术', '机器人技术', '3D打印技术']
        }
      },
      {
        id: 2,
        name: '计算机科学与技术',
        college: 'computer',
        collegeName: '计算机学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: true,
        description: '培养具有良好科学素养，系统掌握计算机科学与技术的基本理论、基本知识和基本技能',
        employment: '98%',
        salary: '10000-15000元',
        tags: ['国家一流专业', '人工智能方向', '高薪就业'],
        courses: {
          core: ['程序设计基础', '数据结构', '计算机组成原理', '操作系统', '数据库系统'],
          practical: ['程序设计实践', '软件工程实践', '毕业设计'],
          elective: ['人工智能', '机器学习', '大数据技术']
        }
      }
    ]
  },

  // 根据ID获取专业
  getMajorById(id) {
    const majors = this.getAllMajors()
    return majors.find(major => major.id === parseInt(id))
  },

  // 根据学院筛选专业
  getMajorsByCollege(collegeId) {
    const majors = this.getAllMajors()
    return majors.filter(major => major.college === collegeId)
  },

  // 搜索专业
  searchMajors(keyword) {
    const majors = this.getAllMajors()
    const lowerKeyword = keyword.toLowerCase()
    return majors.filter(major => 
      major.name.toLowerCase().includes(lowerKeyword) ||
      major.collegeName.toLowerCase().includes(lowerKeyword) ||
      major.description.toLowerCase().includes(lowerKeyword) ||
      major.tags.some(tag => tag.toLowerCase().includes(lowerKeyword))
    )
  }
}

// 师资数据管理
const facultyUtils = {
  // 获取所有教师
  getAllFaculty() {
    return wx.getStorageSync('faculty') || this.getDefaultFaculty()
  },

  // 获取默认师资数据
  getDefaultFaculty() {
    return [
      {
        id: 1,
        name: '陈新',
        title: '中国工程院院士',
        college: '机电工程学院',
        category: 'academician',
        speciality: '机械制造及其自动化',
        avatar: '/images/faculty/chenxin.jpg',
        achievements: ['国家科技进步一等奖', '何梁何利基金科学与技术进步奖'],
        research: '精密制造技术、智能制造系统',
        isStarred: true,
        bio: '中国工程院院士，机械制造及其自动化专家，长期从事精密制造技术研究。',
        publications: ['精密制造技术前沿', '智能制造系统设计'],
        projects: ['国家重点研发计划项目', '国家自然科学基金重点项目']
      },
      {
        id: 2,
        name: '王建华',
        title: '教授、博士生导师',
        college: '计算机学院',
        category: 'professor',
        speciality: '人工智能',
        avatar: '/images/faculty/wangjianhua.jpg',
        achievements: ['国家杰出青年科学基金获得者', '教育部长江学者'],
        research: '机器学习、深度学习、计算机视觉',
        isStarred: true,
        bio: '计算机科学与技术专家，人工智能领域知名学者。',
        publications: ['深度学习理论与实践', '计算机视觉算法'],
        projects: ['国家自然科学基金重点项目', '科技部重点研发计划']
      }
    ]
  },

  // 根据ID获取教师
  getFacultyById(id) {
    const faculty = this.getAllFaculty()
    return faculty.find(f => f.id === parseInt(id))
  },

  // 根据分类筛选教师
  getFacultyByCategory(category) {
    const faculty = this.getAllFaculty()
    return faculty.filter(f => f.category === category)
  },

  // 搜索教师
  searchFaculty(keyword) {
    const faculty = this.getAllFaculty()
    const lowerKeyword = keyword.toLowerCase()
    return faculty.filter(f => 
      f.name.toLowerCase().includes(lowerKeyword) ||
      f.college.toLowerCase().includes(lowerKeyword) ||
      f.speciality.toLowerCase().includes(lowerKeyword) ||
      f.research.toLowerCase().includes(lowerKeyword)
    )
  }
}

// 录取查询工具
const admissionUtils = {
  // 模拟录取查询
  queryAdmission(queryData) {
    const { name, idCard, examNumber, province } = queryData
    
    // 简单验证
    if (!name || !idCard || !examNumber) {
      return {
        success: false,
        message: '请填写完整信息'
      }
    }

    // 模拟查询结果（实际应该调用后端API）
    const isAdmitted = Math.random() > 0.3 // 70%概率被录取
    
    if (isAdmitted) {
      const majors = ['计算机科学与技术', '机械设计制造及其自动化', '自动化', '材料科学与工程']
      const randomMajor = majors[Math.floor(Math.random() * majors.length)]
      
      return {
        success: true,
        admitted: true,
        data: {
          name: name,
          major: randomMajor,
          batch: '本科一批',
          status: '已录取',
          noticeStatus: '录取通知书准备中'
        }
      }
    } else {
      return {
        success: true,
        admitted: false,
        message: '暂未查询到录取信息，请关注征集志愿'
      }
    }
  },

  // 获取历年分数线
  getScoreLines() {
    return {
      '2023': {
        science: { min: 580, avg: 595, max: 620 },
        liberal: { min: 570, avg: 585, max: 610 },
        art: { min: 520, avg: 535, max: 560 }
      },
      '2022': {
        science: { min: 575, avg: 590, max: 615 },
        liberal: { min: 565, avg: 580, max: 605 },
        art: { min: 515, avg: 530, max: 555 }
      },
      '2021': {
        science: { min: 570, avg: 585, max: 610 },
        liberal: { min: 560, avg: 575, max: 600 },
        art: { min: 510, avg: 525, max: 550 }
      }
    }
  },

  // 获取招生政策
  getPolicies() {
    return [
      {
        id: 1,
        title: '2024年招生章程',
        content: '广东工业大学2024年普通高等学校招生章程已发布，详细规定了招生计划、录取规则等重要信息。',
        date: '2024-05-15',
        important: true,
        url: 'https://www.gdut.edu.cn/admission/policy'
      },
      {
        id: 2,
        title: '专业录取规则',
        content: '按照"分数优先，遵循志愿"的原则进行专业录取，不设专业级差。',
        date: '2024-05-10',
        important: false
      }
    ]
  }
}

// 校园信息工具
const campusUtils = {
  // 获取校园信息
  getCampusInfo() {
    return {
      name: '广东工业大学',
      englishName: 'Guangdong University of Technology',
      founded: '1958年',
      type: '公办本科',
      level: '省部共建高校',
      location: '广东省广州市',
      motto: '团结、勤奋、求是、创新',
      campuses: [
        {
          id: 1,
          name: '大学城校区',
          area: '1700亩',
          address: '广州市番禺区大学城外环西路100号',
          features: ['主校区', '现代化建筑群', '完善的教学设施'],
          image: '/images/campus/daxuecheng.jpg'
        },
        {
          id: 2,
          name: '东风路校区',
          address: '广州市越秀区东风东路729号',
          area: '300亩',
          features: ['历史悠久', '市中心位置', '交通便利'],
          image: '/images/campus/dongfenglu.jpg'
        }
      ]
    }
  },

  // 获取学校统计数据
  getSchoolStats() {
    return {
      total: 2800,
      professors: 420,
      associates: 680,
      doctors: 1200,
      academicians: 8,
      students: 50000,
      majors: 80
    }
  }
}

module.exports = {
  majorUtils,
  facultyUtils,
  admissionUtils,
  campusUtils
}
