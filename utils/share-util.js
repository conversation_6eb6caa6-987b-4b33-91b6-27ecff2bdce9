// 分享功能工具类
const shareUtils = {
  // 默认分享配置
  defaultShare: {
    title: '广东工业大学招生小程序',
    desc: '广东工业大学官方招生小程序，提供校园介绍、专业查询、师资介绍、录取自查等服务',
    path: '/pages/index/index',
    imageUrl: '/images/share/default-share.jpg'
  },

  // 页面分享配置
  pageShares: {
    // 首页分享
    index: {
      title: '广东工业大学 - 你的理想大学在这里',
      desc: '探索广工魅力，了解专业设置，查询录取信息，开启你的大学之旅',
      path: '/pages/index/index',
      imageUrl: '/images/share/index-share.jpg'
    },

    // 校园介绍分享
    campus: {
      title: '广东工业大学 - 美丽校园等你来',
      desc: '走进广工校园，感受浓厚的学术氛围和现代化的教学设施',
      path: '/pages/campus/campus',
      imageUrl: '/images/share/campus-share.jpg'
    },

    // 专业介绍分享
    majors: {
      title: '广东工业大学 - 优质专业任你选',
      desc: '80+本科专业，涵盖工、理、经、管、文、法、艺术等学科门类',
      path: '/pages/majors/majors',
      imageUrl: '/images/share/majors-share.jpg'
    },

    // 师资介绍分享
    faculty: {
      title: '广东工业大学 - 名师荟萃育英才',
      desc: '院士领衔，名师云集，为你的成长保驾护航',
      path: '/pages/faculty/faculty',
      imageUrl: '/images/share/faculty-share.jpg'
    },

    // 录取自查分享
    admission: {
      title: '广东工业大学 - 录取查询服务',
      desc: '便捷的录取查询、分数线查看、招生政策了解',
      path: '/pages/admission/admission',
      imageUrl: '/images/share/admission-share.jpg'
    }
  },

  // 获取页面分享配置
  getPageShare(pageName, customData = {}) {
    const baseShare = this.pageShares[pageName] || this.defaultShare
    return {
      ...baseShare,
      ...customData
    }
  },

  // 获取专业详情分享配置
  getMajorShare(major) {
    return {
      title: `${major.name} - 广东工业大学`,
      desc: `了解${major.name}专业详情，就业前景，课程设置等信息`,
      path: `/pages/major-detail/major-detail?id=${major.id}`,
      imageUrl: '/images/share/major-detail-share.jpg'
    }
  },

  // 获取教师详情分享配置
  getFacultyShare(faculty) {
    return {
      title: `${faculty.name} ${faculty.title} - 广东工业大学`,
      desc: `了解${faculty.name}教授的研究方向、学术成就和教学经历`,
      path: `/pages/faculty-detail/faculty-detail?id=${faculty.id}`,
      imageUrl: faculty.avatar || '/images/share/faculty-detail-share.jpg'
    }
  },

  // 生成分享统计
  trackShare(shareType, pageName, shareData) {
    try {
      // 记录分享统计
      const shareStats = wx.getStorageSync('shareStats') || {}
      const today = new Date().toDateString()
      
      if (!shareStats[today]) {
        shareStats[today] = {}
      }
      
      const key = `${pageName}_${shareType}`
      shareStats[today][key] = (shareStats[today][key] || 0) + 1
      
      wx.setStorageSync('shareStats', shareStats)
      
      // 可以在这里添加上报到服务器的逻辑
      console.log('分享统计:', { shareType, pageName, shareData })
    } catch (error) {
      console.error('分享统计记录失败:', error)
    }
  },

  // 处理分享成功回调
  onShareSuccess(shareType, pageName, shareData) {
    this.trackShare(shareType, pageName, shareData)
    
    wx.showToast({
      title: '分享成功',
      icon: 'success',
      duration: 1500
    })
  },

  // 处理分享失败回调
  onShareFail(shareType, pageName, error) {
    console.error('分享失败:', { shareType, pageName, error })
    
    wx.showToast({
      title: '分享失败，请重试',
      icon: 'none',
      duration: 2000
    })
  },

  // 获取分享统计数据
  getShareStats() {
    try {
      return wx.getStorageSync('shareStats') || {}
    } catch (error) {
      console.error('获取分享统计失败:', error)
      return {}
    }
  },

  // 清理过期的分享统计数据（保留最近30天）
  cleanupShareStats() {
    try {
      const shareStats = this.getShareStats()
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const cleanedStats = {}
      Object.keys(shareStats).forEach(dateStr => {
        const date = new Date(dateStr)
        if (date >= thirtyDaysAgo) {
          cleanedStats[dateStr] = shareStats[dateStr]
        }
      })
      
      wx.setStorageSync('shareStats', cleanedStats)
    } catch (error) {
      console.error('清理分享统计失败:', error)
    }
  }
}

// SEO优化工具
const seoUtils = {
  // 页面SEO配置
  pageSEO: {
    index: {
      title: '广东工业大学招生小程序 - 官方招生信息平台',
      keywords: '广东工业大学,招生,专业介绍,师资力量,录取查询,大学,高考',
      description: '广东工业大学官方招生小程序，提供最新招生政策、专业介绍、师资力量、校园风光、录取查询等服务，助力考生了解广工，选择理想专业。'
    },
    
    campus: {
      title: '校园介绍 - 广东工业大学',
      keywords: '广东工业大学,校园介绍,校园风光,大学城,历史沿革',
      description: '了解广东工业大学美丽校园，包括大学城校区、东风路校区等，感受浓厚的学术氛围和现代化的教学设施。'
    },
    
    majors: {
      title: '专业介绍 - 广东工业大学招生',
      keywords: '广东工业大学,专业介绍,本科专业,工科专业,就业前景',
      description: '广东工业大学80+本科专业介绍，涵盖工、理、经、管、文、法、艺术等学科门类，了解专业详情和就业前景。'
    },
    
    faculty: {
      title: '师资介绍 - 广东工业大学',
      keywords: '广东工业大学,师资力量,教授,院士,名师,教学团队',
      description: '广东工业大学优秀师资团队介绍，院士领衔，名师云集，为学生成长成才提供有力保障。'
    },
    
    admission: {
      title: '录取查询 - 广东工业大学招生',
      keywords: '广东工业大学,录取查询,分数线,招生政策,高考录取',
      description: '广东工业大学录取查询服务，提供录取结果查询、历年分数线、招生政策、招生计划等信息。'
    }
  },

  // 设置页面SEO信息
  setPageSEO(pageName, customSEO = {}) {
    const seoConfig = this.pageSEO[pageName] || this.pageSEO.index
    const finalSEO = { ...seoConfig, ...customSEO }
    
    // 设置页面标题
    if (finalSEO.title) {
      wx.setNavigationBarTitle({
        title: finalSEO.title
      })
    }
    
    // 在实际项目中，这里可以设置页面的meta信息
    // 小程序暂时不支持直接设置meta，但可以为后续H5版本做准备
    return finalSEO
  },

  // 生成结构化数据
  generateStructuredData(type, data) {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': type
    }
    
    switch (type) {
      case 'EducationalOrganization':
        return {
          ...structuredData,
          name: data.name || '广东工业大学',
          url: 'https://www.gdut.edu.cn',
          logo: 'https://www.gdut.edu.cn/logo.png',
          address: {
            '@type': 'PostalAddress',
            streetAddress: '大学城外环西路100号',
            addressLocality: '广州市',
            addressRegion: '广东省',
            addressCountry: 'CN'
          },
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: '+86-20-39322681',
            contactType: '招生咨询'
          }
        }
        
      case 'Course':
        return {
          ...structuredData,
          name: data.name,
          description: data.description,
          provider: {
            '@type': 'EducationalOrganization',
            name: '广东工业大学'
          }
        }
        
      case 'Person':
        return {
          ...structuredData,
          name: data.name,
          jobTitle: data.title,
          worksFor: {
            '@type': 'EducationalOrganization',
            name: '广东工业大学'
          }
        }
        
      default:
        return structuredData
    }
  }
}

module.exports = {
  shareUtils,
  seoUtils
}
