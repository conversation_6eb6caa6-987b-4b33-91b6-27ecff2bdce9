name: CD

on:
  push:
    tags:
    - "v*"

jobs:
  scf-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 
        uses: actions/checkout@master
      - name: Write private key to file
        run: |
          echo "${{ secrets.MP_PRIVATE_KEY }}" > ./private_key
          chmod 600 ./private_key
      - name: Build 
        uses: docker://tencentcom/miniprogram-ci:latest
        env: 
          PLUGIN_APPID: ${{ vars.MP_ID}}
          PLUGIN_PROJECTPATH: ./
          PLUGIN_PRIVATEKEYPATH: ./private_key
          PLUGIN_VERSION: ${{ github.ref_name }}
