// 广东工业大学招生小程序测试脚本
const { majorUtils, facultyUtils, admissionUtils, campusUtils } = require('./utils/admission-util')

console.log('🎓 广东工业大学招生小程序测试开始...\n')

// 测试专业数据
console.log('📚 测试专业数据管理:')
try {
  const majors = majorUtils.getAllMajors()
  console.log(`✅ 获取专业列表成功，共 ${majors.length} 个专业`)
  
  const computerMajor = majorUtils.getMajorById(2)
  if (computerMajor) {
    console.log(`✅ 获取专业详情成功: ${computerMajor.name}`)
  }
  
  const mechanicalMajors = majorUtils.getMajorsByCollege('mechanical')
  console.log(`✅ 按学院筛选成功，机电工程学院有 ${mechanicalMajors.length} 个专业`)
  
  const searchResults = majorUtils.searchMajors('计算机')
  console.log(`✅ 专业搜索成功，找到 ${searchResults.length} 个相关专业`)
} catch (error) {
  console.error('❌ 专业数据测试失败:', error.message)
}

console.log('\n👨‍🏫 测试师资数据管理:')
try {
  const faculty = facultyUtils.getAllFaculty()
  console.log(`✅ 获取师资列表成功，共 ${faculty.length} 位教师`)
  
  const professor = facultyUtils.getFacultyById(1)
  if (professor) {
    console.log(`✅ 获取教师详情成功: ${professor.name} - ${professor.title}`)
  }
  
  const academicians = facultyUtils.getFacultyByCategory('academician')
  console.log(`✅ 按分类筛选成功，院士有 ${academicians.length} 位`)
  
  const searchResults = facultyUtils.searchFaculty('陈新')
  console.log(`✅ 教师搜索成功，找到 ${searchResults.length} 位相关教师`)
} catch (error) {
  console.error('❌ 师资数据测试失败:', error.message)
}

console.log('\n🔍 测试录取查询功能:')
try {
  const queryData = {
    name: '张三',
    idCard: '123456',
    examNumber: '2024001',
    province: '广东省'
  }
  
  const result = admissionUtils.queryAdmission(queryData)
  if (result.success) {
    console.log(`✅ 录取查询成功: ${result.admitted ? '已录取' : '未录取'}`)
  }
  
  const scoreLines = admissionUtils.getScoreLines()
  console.log(`✅ 获取分数线成功，共 ${Object.keys(scoreLines).length} 年数据`)
  
  const policies = admissionUtils.getPolicies()
  console.log(`✅ 获取招生政策成功，共 ${policies.length} 条政策`)
} catch (error) {
  console.error('❌ 录取查询测试失败:', error.message)
}

console.log('\n🏫 测试校园信息功能:')
try {
  const campusInfo = campusUtils.getCampusInfo()
  console.log(`✅ 获取校园信息成功: ${campusInfo.name}`)
  console.log(`   建校时间: ${campusInfo.founded}`)
  console.log(`   校区数量: ${campusInfo.campuses.length} 个`)
  
  const stats = campusUtils.getSchoolStats()
  console.log(`✅ 获取学校统计成功: 教职工 ${stats.total} 人，学生 ${stats.students} 人`)
} catch (error) {
  console.error('❌ 校园信息测试失败:', error.message)
}

console.log('\n📱 测试小程序页面配置:')
try {
  const fs = require('fs')
  
  // 检查app.json配置
  const appConfig = JSON.parse(fs.readFileSync('app.json', 'utf8'))
  console.log(`✅ app.json 配置正确，共 ${appConfig.pages.length} 个页面`)
  console.log(`   底部导航: ${appConfig.tabBar.list.length} 个标签`)
  
  // 检查主要页面文件
  const requiredPages = [
    'pages/index/index',
    'pages/campus/campus',
    'pages/majors/majors',
    'pages/faculty/faculty',
    'pages/admission/admission'
  ]
  
  let pageCount = 0
  for (const page of requiredPages) {
    const jsFile = `${page}.js`
    const wxmlFile = `${page}.wxml`
    const wxssFile = `${page}.wxss`
    const jsonFile = `${page}.json`
    
    if (fs.existsSync(jsFile) && fs.existsSync(wxmlFile) && 
        fs.existsSync(wxssFile) && fs.existsSync(jsonFile)) {
      pageCount++
    }
  }
  
  console.log(`✅ 页面文件检查完成，${pageCount}/${requiredPages.length} 个页面文件完整`)
  
} catch (error) {
  console.error('❌ 页面配置测试失败:', error.message)
}

console.log('\n🎉 测试完成！')
console.log('\n📋 功能清单:')
console.log('✅ 首页 - 学校介绍和快捷导航')
console.log('✅ 校园介绍 - 校园风光、历史沿革、设施介绍')
console.log('✅ 专业介绍 - 专业列表、搜索筛选、详情查看')
console.log('✅ 师资介绍 - 教师列表、分类筛选、详情查看')
console.log('✅ 录取自查 - 录取查询、分数线、招生政策')
console.log('✅ 设置页面 - 联系方式、意见反馈、关于信息')

console.log('\n🚀 广东工业大学招生小程序开发完成！')
console.log('💡 建议后续优化:')
console.log('   1. 添加真实的图片资源')
console.log('   2. 接入后端API获取实时数据')
console.log('   3. 添加更多交互动画效果')
console.log('   4. 优化页面加载性能')
console.log('   5. 添加用户行为统计分析')

// 如果是直接运行脚本
if (require.main === module) {
  console.log('\n📝 注意事项:')
  console.log('   - 请确保在微信开发者工具中测试')
  console.log('   - 图片资源需要替换为真实图片')
  console.log('   - 数据可以根据实际需求调整')
  console.log('   - 建议添加错误处理和用户反馈')
}
