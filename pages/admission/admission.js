// pages/admission/admission.js
Page({
  data: {
    activeTab: 'query',
    tabs: [
      { id: 'query', name: '录取查询', icon: '🔍' },
      { id: 'scores', name: '分数线', icon: '📊' },
      { id: 'policy', name: '招生政策', icon: '📋' },
      { id: 'calendar', name: '招生日程', icon: '📅' }
    ],
    // 查询表单数据
    queryForm: {
      name: '',
      idCard: '',
      examNumber: '',
      province: ''
    },
    provinces: [
      '广东省', '湖南省', '湖北省', '河南省', '河北省', '山东省', 
      '江苏省', '浙江省', '安徽省', '江西省', '福建省', '四川省',
      '重庆市', '云南省', '贵州省', '广西壮族自治区', '海南省'
    ],
    // 历年分数线数据
    scoreLines: {
      '2023': {
        science: { min: 580, avg: 595, max: 620 },
        liberal: { min: 570, avg: 585, max: 610 },
        art: { min: 520, avg: 535, max: 560 }
      },
      '2022': {
        science: { min: 575, avg: 590, max: 615 },
        liberal: { min: 565, avg: 580, max: 605 },
        art: { min: 515, avg: 530, max: 555 }
      },
      '2021': {
        science: { min: 570, avg: 585, max: 610 },
        liberal: { min: 560, avg: 575, max: 600 },
        art: { min: 510, avg: 525, max: 550 }
      }
    },
    // 招生政策
    policies: [
      {
        title: '2024年招生章程',
        content: '广东工业大学2024年普通高等学校招生章程已发布，详细规定了招生计划、录取规则等重要信息。',
        date: '2024-05-15',
        important: true
      },
      {
        title: '专业录取规则',
        content: '按照"分数优先，遵循志愿"的原则进行专业录取，不设专业级差。',
        date: '2024-05-10',
        important: false
      },
      {
        title: '加分政策说明',
        content: '认可各省（区、市）教育主管部门根据教育部相关规定给予考生的全国性加分项目和分值。',
        date: '2024-05-08',
        important: false
      }
    ],
    // 招生日程
    calendar: [
      {
        date: '6月7-8日',
        event: '全国高考',
        status: 'completed',
        description: '2024年全国统一高考'
      },
      {
        date: '6月25日',
        event: '高考成绩公布',
        status: 'completed',
        description: '各省陆续公布高考成绩'
      },
      {
        date: '6月28日-7月2日',
        event: '志愿填报',
        status: 'active',
        description: '本科批次志愿填报时间'
      },
      {
        date: '7月15日-7月25日',
        event: '录取工作',
        status: 'pending',
        description: '本科批次录取工作进行'
      },
      {
        date: '8月底',
        event: '新生报到',
        status: 'pending',
        description: '2024级新生入学报到'
      }
    ]
  },

  onLoad() {
    // 页面加载时的初始化
  },

  // 切换标签页
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`queryForm.${field}`]: value
    })
  },

  // 选择省份
  onProvinceChange(e) {
    const index = e.detail.value
    this.setData({
      'queryForm.province': this.data.provinces[index]
    })
  },

  // 提交查询
  submitQuery() {
    const { queryForm } = this.data
    
    // 验证表单
    if (!queryForm.name || !queryForm.idCard || !queryForm.examNumber) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    // 模拟查询过程
    wx.showLoading({
      title: '查询中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      
      // 模拟查询结果
      const isAdmitted = Math.random() > 0.3 // 70%概率被录取
      
      if (isAdmitted) {
        const majors = ['计算机科学与技术', '机械设计制造及其自动化', '自动化', '材料科学与工程']
        const randomMajor = majors[Math.floor(Math.random() * majors.length)]
        
        wx.showModal({
          title: '查询结果',
          content: `恭喜！您已被广东工业大学录取\n\n录取专业：${randomMajor}\n录取批次：本科一批\n\n请关注录取通知书邮寄信息`,
          showCancel: false,
          confirmText: '太好了！'
        })
      } else {
        wx.showModal({
          title: '查询结果',
          content: '很遗憾，暂未查询到您的录取信息。\n\n建议：\n1. 确认信息填写正确\n2. 关注征集志愿信息\n3. 考虑其他批次录取',
          showCancel: false,
          confirmText: '知道了'
        })
      }
    }, 2000)
  },

  // 查看分数线详情
  viewScoreDetail(e) {
    const { year, type } = e.currentTarget.dataset
    const score = this.data.scoreLines[year][type]
    
    let typeName = ''
    switch(type) {
      case 'science': typeName = '理科'; break
      case 'liberal': typeName = '文科'; break
      case 'art': typeName = '艺术类'; break
    }
    
    wx.showModal({
      title: `${year}年${typeName}分数线`,
      content: `最低分：${score.min}分\n平均分：${score.avg}分\n最高分：${score.max}分`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看政策详情
  viewPolicyDetail(e) {
    const { policy } = e.currentTarget.dataset
    wx.showModal({
      title: policy.title,
      content: policy.content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '广东工业大学 - 录取自查',
      path: '/pages/admission/admission'
    }
  }
})
