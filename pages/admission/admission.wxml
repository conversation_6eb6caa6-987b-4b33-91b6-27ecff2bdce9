<!--pages/admission/admission.wxml-->
<view class="container">
  <!-- 标签页导航 -->
  <view class="tabs-section">
    <view class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
          wx:for="{{tabs}}" 
          wx:key="id" 
          bindtap="switchTab" 
          data-tab="{{item.id}}">
      <text class="tab-icon">{{item.icon}}</text>
      <text class="tab-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 录取查询 -->
  <view class="content-section" wx:if="{{activeTab === 'query'}}">
    <view class="query-content">
      <view class="query-header">
        <view class="header-icon">🎓</view>
        <view class="header-title">录取结果查询</view>
        <view class="header-desc">请输入准确信息查询录取结果</view>
      </view>

      <view class="query-form">
        <view class="form-item">
          <view class="form-label">姓名</view>
          <input class="form-input" placeholder="请输入考生姓名" value="{{queryForm.name}}" bindinput="onInputChange" data-field="name" />
        </view>

        <view class="form-item">
          <view class="form-label">身份证号</view>
          <input class="form-input" placeholder="请输入身份证号后6位" value="{{queryForm.idCard}}" bindinput="onInputChange" data-field="idCard" maxlength="6" />
        </view>

        <view class="form-item">
          <view class="form-label">考生号</view>
          <input class="form-input" placeholder="请输入考生号" value="{{queryForm.examNumber}}" bindinput="onInputChange" data-field="examNumber" />
        </view>

        <view class="form-item">
          <view class="form-label">生源省份</view>
          <picker mode="selector" range="{{provinces}}" bindchange="onProvinceChange">
            <view class="form-picker">
              <text class="picker-text {{queryForm.province ? '' : 'placeholder'}}">
                {{queryForm.province || '请选择生源省份'}}
              </text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>

        <button class="query-btn" bindtap="submitQuery">查询录取结果</button>
      </view>

      <view class="query-tips">
        <view class="tips-title">查询说明</view>
        <view class="tips-list">
          <view class="tip-item">• 请确保输入信息准确无误</view>
          <view class="tip-item">• 录取结果将在录取工作结束后更新</view>
          <view class="tip-item">• 如有疑问请联系招生办公室</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 历年分数线 -->
  <view class="content-section" wx:if="{{activeTab === 'scores'}}">
    <view class="scores-content">
      <view class="scores-header">
        <view class="header-icon">📊</view>
        <view class="header-title">历年录取分数线</view>
        <view class="header-desc">广东省录取分数线统计</view>
      </view>

      <view class="scores-list">
        <view class="year-section" wx:for="{{scoreLines}}" wx:key="year" wx:for-item="yearData" wx:for-index="year">
          <view class="year-title">{{year}}年录取分数线</view>
          <view class="score-cards">
            <view class="score-card" bindtap="viewScoreDetail" data-year="{{year}}" data-type="science">
              <view class="score-type">理科</view>
              <view class="score-range">{{yearData.science.min}} - {{yearData.science.max}}</view>
              <view class="score-avg">平均分：{{yearData.science.avg}}</view>
            </view>
            <view class="score-card" bindtap="viewScoreDetail" data-year="{{year}}" data-type="liberal">
              <view class="score-type">文科</view>
              <view class="score-range">{{yearData.liberal.min}} - {{yearData.liberal.max}}</view>
              <view class="score-avg">平均分：{{yearData.liberal.avg}}</view>
            </view>
            <view class="score-card" bindtap="viewScoreDetail" data-year="{{year}}" data-type="art">
              <view class="score-type">艺术类</view>
              <view class="score-range">{{yearData.art.min}} - {{yearData.art.max}}</view>
              <view class="score-avg">平均分：{{yearData.art.avg}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 招生政策 -->
  <view class="content-section" wx:if="{{activeTab === 'policy'}}">
    <view class="policy-content">
      <view class="policy-header">
        <view class="header-icon">📋</view>
        <view class="header-title">招生政策</view>
        <view class="header-desc">最新招生政策和规定</view>
      </view>

      <view class="policy-list">
        <view class="policy-item {{item.important ? 'important' : ''}}" wx:for="{{policies}}" wx:key="title" bindtap="viewPolicyDetail" data-policy="{{item}}">
          <view class="policy-title">
            <text class="important-badge" wx:if="{{item.important}}">重要</text>
            {{item.title}}
          </view>
          <view class="policy-content-preview">{{item.content}}</view>
          <view class="policy-date">发布时间：{{item.date}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 招生日程 -->
  <view class="content-section" wx:if="{{activeTab === 'calendar'}}">
    <view class="calendar-content">
      <view class="calendar-header">
        <view class="header-icon">📅</view>
        <view class="header-title">招生日程</view>
        <view class="header-desc">2024年招生工作时间安排</view>
      </view>

      <view class="timeline">
        <view class="timeline-item {{item.status}}" wx:for="{{calendar}}" wx:key="date">
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <view class="timeline-date">{{item.date}}</view>
            <view class="timeline-event">{{item.event}}</view>
            <view class="timeline-desc">{{item.description}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
