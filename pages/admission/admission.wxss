/* pages/admission/admission.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页导航 */
.tabs-section {
  background: white;
  display: flex;
  border-bottom: 1rpx solid #eee;
  padding: 0 20rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  border-bottom-color: #1E88E5;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-name {
  font-size: 24rpx;
  color: #666;
}

.tab-item.active .tab-name {
  color: #1E88E5;
  font-weight: bold;
}

/* 内容区域 */
.content-section {
  padding: 30rpx;
}

/* 通用头部样式 */
.query-header, .scores-header, .policy-header, .calendar-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.header-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 26rpx;
  color: #666;
}

/* 查询表单样式 */
.query-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.query-form {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f9fa;
  transition: border-color 0.3s;
}

.form-input:focus {
  border-color: #1E88E5;
}

.form-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  border: 2rpx solid #f8f9fa;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-text.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #666;
}

.query-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-top: 30rpx;
}

.query-btn::after {
  border: none;
}

/* 查询提示 */
.query-tips {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-list {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

.tip-item {
  margin-bottom: 10rpx;
}

/* 分数线样式 */
.scores-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.year-section {
  margin-bottom: 40rpx;
}

.year-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 25rpx;
  text-align: center;
}

.score-cards {
  display: flex;
  gap: 20rpx;
}

.score-card {
  flex: 1;
  background: linear-gradient(135deg, #E3F2FD, #F3E5F5);
  border-radius: 15rpx;
  padding: 25rpx;
  text-align: center;
}

.score-type {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.score-range {
  font-size: 30rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 8rpx;
}

.score-avg {
  font-size: 22rpx;
  color: #666;
}

/* 政策样式 */
.policy-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.policy-item {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #ddd;
}

.policy-item.important {
  border-left-color: #FF6B6B;
  background: #FFF5F5;
}

.policy-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.important-badge {
  background: #FF6B6B;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  margin-right: 15rpx;
}

.policy-content-preview {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.policy-date {
  font-size: 22rpx;
  color: #999;
}

/* 时间轴样式 */
.calendar-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: #E0E0E0;
}

.timeline-item {
  position: relative;
  padding: 20rpx 0 20rpx 80rpx;
  margin-bottom: 30rpx;
}

.timeline-dot {
  position: absolute;
  left: 22rpx;
  top: 30rpx;
  width: 16rpx;
  height: 16rpx;
  background: #E0E0E0;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 0 0 4rpx #E0E0E0;
}

.timeline-item.completed .timeline-dot {
  background: #4CAF50;
  box-shadow: 0 0 0 4rpx #4CAF50;
}

.timeline-item.active .timeline-dot {
  background: #1E88E5;
  box-shadow: 0 0 0 4rpx #1E88E5;
}

.timeline-content {
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 15rpx;
}

.timeline-item.completed .timeline-content {
  background: #E8F5E8;
}

.timeline-item.active .timeline-content {
  background: #E3F2FD;
}

.timeline-date {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.timeline-event {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.timeline-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
