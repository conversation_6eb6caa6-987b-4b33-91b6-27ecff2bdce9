// pages/majors/majors.js
const { favoriteUtils, historyUtils, analyticsUtils } = require('../../utils/favorite-util')
const { shareUtils } = require('../../utils/share-util')

Page({
  data: {
    searchKeyword: '',
    selectedCollege: 'all',
    colleges: [
      { id: 'all', name: '全部学院' },
      { id: 'mechanical', name: '机电工程学院' },
      { id: 'automation', name: '自动化学院' },
      { id: 'computer', name: '计算机学院' },
      { id: 'materials', name: '材料与能源学院' },
      { id: 'civil', name: '土木与交通工程学院' },
      { id: 'management', name: '管理学院' },
      { id: 'economics', name: '经济与贸易学院' },
      { id: 'foreign', name: '外国语学院' },
      { id: 'art', name: '艺术与设计学院' },
      { id: 'applied', name: '应用数学学院' }
    ],
    majors: [
      {
        id: 1,
        name: '机械设计制造及其自动化',
        college: 'mechanical',
        collegeName: '机电工程学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: true,
        description: '培养具备机械设计制造基础知识与应用能力的高级工程技术人才',
        employment: '95%',
        salary: '8000-12000元',
        tags: ['国家特色专业', '工程认证', '就业热门']
      },
      {
        id: 2,
        name: '计算机科学与技术',
        college: 'computer',
        collegeName: '计算机学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: true,
        description: '培养具有良好科学素养，系统掌握计算机科学与技术的基本理论、基本知识和基本技能',
        employment: '98%',
        salary: '10000-15000元',
        tags: ['国家一流专业', '人工智能方向', '高薪就业']
      },
      {
        id: 3,
        name: '自动化',
        college: 'automation',
        collegeName: '自动化学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: true,
        description: '培养具备电工技术、电子技术、控制理论、自动检测与仪表等较宽广领域的工程技术基础',
        employment: '96%',
        salary: '9000-13000元',
        tags: ['省级特色专业', '智能制造', '就业面广']
      },
      {
        id: 4,
        name: '材料科学与工程',
        college: 'materials',
        collegeName: '材料与能源学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: false,
        description: '培养具备材料科学与工程方面的基本理论、基本知识和基本技能',
        employment: '92%',
        salary: '7000-11000元',
        tags: ['新材料方向', '科研导向', '前景广阔']
      },
      {
        id: 5,
        name: '土木工程',
        college: 'civil',
        collegeName: '土木与交通工程学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: false,
        description: '培养掌握土木工程学科的基本理论和基本知识，具备从事土木工程的项目规划、设计、研究开发、施工及管理的能力',
        employment: '94%',
        salary: '8000-12000元',
        tags: ['基础设施建设', '工程认证', '稳定就业']
      },
      {
        id: 6,
        name: '工商管理',
        college: 'management',
        collegeName: '管理学院',
        level: '本科',
        duration: '4年',
        degree: '管理学学士',
        category: '管理学',
        isKey: false,
        description: '培养具备管理、经济、法律及企业管理方面的知识和能力',
        employment: '90%',
        salary: '6000-10000元',
        tags: ['管理精英', '创业导向', '综合发展']
      },
      {
        id: 7,
        name: '国际经济与贸易',
        college: 'economics',
        collegeName: '经济与贸易学院',
        level: '本科',
        duration: '4年',
        degree: '经济学学士',
        category: '经济学',
        isKey: false,
        description: '培养具有较强的国际贸易实际能力，主要从事进出口业务、外贸企业管理、国际经济技术合作等工作',
        employment: '88%',
        salary: '7000-11000元',
        tags: ['国际化', '外贸导向', '语言优势']
      },
      {
        id: 8,
        name: '英语',
        college: 'foreign',
        collegeName: '外国语学院',
        level: '本科',
        duration: '4年',
        degree: '文学学士',
        category: '文学',
        isKey: false,
        description: '培养具有扎实的英语语言基础和比较广泛的科学文化知识',
        employment: '85%',
        salary: '6000-9000元',
        tags: ['语言技能', '国际交流', '教育培训']
      },
      {
        id: 9,
        name: '工业设计',
        college: 'art',
        collegeName: '艺术与设计学院',
        level: '本科',
        duration: '4年',
        degree: '工学学士',
        category: '工学',
        isKey: false,
        description: '培养具备工业设计的基础理论、知识与应用能力',
        employment: '87%',
        salary: '7000-12000元',
        tags: ['创意设计', '产品开发', '美学与技术']
      },
      {
        id: 10,
        name: '应用数学',
        college: 'applied',
        collegeName: '应用数学学院',
        level: '本科',
        duration: '4年',
        degree: '理学学士',
        category: '理学',
        isKey: false,
        description: '培养掌握数学科学的基本理论与基本方法，具备运用数学知识、使用计算机解决实际问题的能力',
        employment: '91%',
        salary: '8000-13000元',
        tags: ['数据分析', '金融数学', '算法优化']
      }
    ],
    filteredMajors: []
  },

  onLoad() {
    this.initData()

    // 记录页面访问
    analyticsUtils.trackEvent('page_visit', {
      page: 'majors',
      timestamp: new Date().toISOString()
    })
  },

  onShow() {
    this.updateMajorFavoriteStatus()
  },

  // 初始化数据
  initData() {
    // 为专业添加收藏状态
    const majorsWithFavorite = this.data.majors.map(major => ({
      ...major,
      isFavorited: favoriteUtils.isFavorited(favoriteUtils.TYPES.MAJOR, major.id)
    }))

    this.setData({
      majors: majorsWithFavorite,
      filteredMajors: majorsWithFavorite
    })
  },

  // 搜索专业
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    this.filterMajors()
  },

  // 选择学院
  onCollegeChange(e) {
    const collegeId = e.currentTarget.dataset.college
    this.setData({
      selectedCollege: collegeId
    })
    this.filterMajors()
  },

  // 过滤专业
  filterMajors() {
    const { majors, searchKeyword, selectedCollege } = this.data
    let filtered = majors

    // 按学院过滤
    if (selectedCollege !== 'all') {
      filtered = filtered.filter(major => major.college === selectedCollege)
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase()
      filtered = filtered.filter(major => 
        major.name.toLowerCase().includes(keyword) ||
        major.collegeName.toLowerCase().includes(keyword) ||
        major.description.toLowerCase().includes(keyword) ||
        major.tags.some(tag => tag.toLowerCase().includes(keyword))
      )
    }

    this.setData({
      filteredMajors: filtered
    })
  },

  // 查看专业详情
  viewMajorDetail(e) {
    const { major } = e.currentTarget.dataset

    // 添加到历史记录
    historyUtils.addHistory(historyUtils.TYPES.MAJOR_VIEW, major)

    // 记录用户行为
    analyticsUtils.trackEvent('major_view', {
      majorId: major.id,
      majorName: major.name,
      source: 'majors_list'
    })

    wx.navigateTo({
      url: `/pages/major-detail/major-detail?id=${major.id}`
    })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    e.stopPropagation()
    const { major } = e.currentTarget.dataset

    const success = favoriteUtils.toggleFavorite(favoriteUtils.TYPES.MAJOR, major)
    if (success) {
      // 更新页面数据中的收藏状态
      this.updateMajorFavoriteStatus()

      // 记录用户行为
      const isFavorited = favoriteUtils.isFavorited(favoriteUtils.TYPES.MAJOR, major.id)
      analyticsUtils.trackEvent(isFavorited ? 'add_favorite' : 'remove_favorite', {
        type: 'major',
        itemId: major.id,
        itemName: major.name
      })
    }
  },

  // 更新专业收藏状态
  updateMajorFavoriteStatus() {
    const { majors, filteredMajors } = this.data

    // 更新所有专业的收藏状态
    const updatedMajors = majors.map(major => ({
      ...major,
      isFavorited: favoriteUtils.isFavorited(favoriteUtils.TYPES.MAJOR, major.id)
    }))

    const updatedFilteredMajors = filteredMajors.map(major => ({
      ...major,
      isFavorited: favoriteUtils.isFavorited(favoriteUtils.TYPES.MAJOR, major.id)
    }))

    this.setData({
      majors: updatedMajors,
      filteredMajors: updatedFilteredMajors
    })
  },

  // 清空搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    })
    this.filterMajors()
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '广东工业大学 - 专业介绍',
      path: '/pages/majors/majors'
    }
  }
})
