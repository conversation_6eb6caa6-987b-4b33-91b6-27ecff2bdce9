/* pages/majors/majors.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏样式 */
.search-section {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  position: relative;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 50rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-icon, .clear-icon {
  position: absolute;
  right: 25rpx;
  font-size: 28rpx;
  color: #999;
}

.clear-icon {
  background: #ccc;
  color: white;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

/* 筛选栏样式 */
.filter-section {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.college-filter {
  white-space: nowrap;
  padding: 0 20rpx;
}

.college-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.college-item.active {
  background: #1E88E5;
  color: white;
  border-color: #1E88E5;
}

/* 专业列表样式 */
.majors-section {
  padding: 20rpx;
}

.majors-count {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}

.majors-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.major-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.major-card:active {
  transform: scale(0.98);
}

/* 专业头部 */
.major-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.major-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1E88E5;
  flex: 1;
}

.key-major-badge {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

/* 基本信息 */
.major-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.info-label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  flex: 1;
}

.info-value.employment {
  color: #4CAF50;
  font-weight: bold;
}

.info-value.salary {
  color: #FF9800;
  font-weight: bold;
}

/* 专业描述 */
.major-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
}

/* 标签 */
.major-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.tag {
  background: #E3F2FD;
  color: #1E88E5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 查看详情按钮 */
.view-detail-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 12rpx;
  color: white;
  font-size: 26rpx;
}

.arrow {
  font-size: 28rpx;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}
