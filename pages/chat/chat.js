// pages/chat/chat.js
const { analyticsUtils } = require('../../utils/favorite-util')

Page({
  data: {
    messages: [],
    inputText: '',
    isTyping: false,
    quickQuestions: [
      '学校有哪些特色专业？',
      '录取分数线是多少？',
      '学费和住宿费用如何？',
      '有哪些奖学金政策？',
      '就业情况怎么样？',
      '如何报考贵校？'
    ],
    // 预设的问答库
    qaDatabase: {
      '学校有哪些特色专业': {
        answer: '广东工业大学拥有多个特色专业，包括：\n\n🔥 国家特色专业：\n• 机械设计制造及其自动化\n• 自动化\n• 化学工程与工艺\n• 材料成型及控制工程\n\n⭐ 国家一流专业：\n• 计算机科学与技术\n• 软件工程\n• 电气工程及其自动化\n• 土木工程\n\n这些专业在就业市场上都有很好的前景！',
        relatedQuestions: ['就业情况怎么样', '录取分数线是多少']
      },
      '录取分数线是多少': {
        answer: '近三年广东省录取分数线参考：\n\n📊 2023年：\n• 理科：最低580分，平均595分\n• 文科：最低570分，平均585分\n\n📊 2022年：\n• 理科：最低575分，平均590分\n• 文科：最低565分，平均580分\n\n📊 2021年：\n• 理科：最低570分，平均585分\n• 文科：最低560分，平均575分\n\n具体分数线会根据当年情况有所调整，建议关注官方最新信息。',
        relatedQuestions: ['如何报考贵校', '有哪些奖学金政策']
      },
      '学费和住宿费用如何': {
        answer: '💰 收费标准（每年）：\n\n📚 学费：\n• 普通专业：5510-6230元\n• 艺术类专业：10000元\n• 中外合作办学：28000-35000元\n\n🏠 住宿费：\n• 学生宿舍：1200-1500元\n• 公寓式宿舍：1500-2000元\n\n💡 其他费用：\n• 教材费：约500-800元\n• 体检费：约100元\n\n学校还提供多种奖学金和助学金，帮助家庭困难学生完成学业。',
        relatedQuestions: ['有哪些奖学金政策', '学校有哪些特色专业']
      },
      '有哪些奖学金政策': {
        answer: '🏆 奖学金体系：\n\n💎 国家级奖学金：\n• 国家奖学金：8000元/年\n• 国家励志奖学金：5000元/年\n• 国家助学金：2000-4000元/年\n\n🌟 学校奖学金：\n• 校长奖学金：10000元/年\n• 优秀学生奖学金：500-3000元/年\n• 单项奖学金：200-1000元/年\n\n🎯 专项奖学金：\n• 企业奖学金\n• 校友奖学金\n• 创新创业奖学金\n\n覆盖面达到30%以上的学生！',
        relatedQuestions: ['学费和住宿费用如何', '就业情况怎么样']
      },
      '就业情况怎么样': {
        answer: '📈 就业数据亮眼：\n\n✅ 就业率：连续多年保持95%以上\n\n🏢 就业去向：\n• 知名企业：华为、腾讯、比亚迪、格力等\n• 国有企业：中建、中铁、南方电网等\n• 事业单位：各级政府机关、科研院所\n• 继续深造：国内外知名高校读研\n\n💰 薪资水平：\n• 平均起薪：8000-12000元\n• 热门专业：10000-15000元\n\n🌟 就业质量持续提升，用人单位满意度高！',
        relatedQuestions: ['学校有哪些特色专业', '录取分数线是多少']
      },
      '如何报考贵校': {
        answer: '📝 报考流程：\n\n1️⃣ 高考报名\n• 参加全国统一高考\n• 关注各省招生计划\n\n2️⃣ 志愿填报\n• 在本科一批填报我校\n• 建议填报多个专业志愿\n• 服从专业调剂\n\n3️⃣ 录取查询\n• 关注录取进度\n• 及时查询录取结果\n\n4️⃣ 入学准备\n• 收到录取通知书\n• 按时报到注册\n\n💡 建议提前了解专业设置，合理填报志愿！',
        relatedQuestions: ['录取分数线是多少', '学校有哪些特色专业']
      }
    }
  },

  onLoad() {
    this.initChat()
    
    // 记录页面访问
    analyticsUtils.trackEvent('page_visit', {
      page: 'chat',
      timestamp: new Date().toISOString()
    })
  },

  // 初始化聊天
  initChat() {
    const welcomeMessage = {
      id: Date.now(),
      type: 'bot',
      content: '您好！我是广东工业大学招生咨询助手 🤖\n\n我可以为您解答关于学校专业、录取分数、学费住宿、奖学金政策等问题。\n\n请选择您感兴趣的问题，或直接输入您的疑问：',
      timestamp: new Date().toISOString()
    }

    this.setData({
      messages: [welcomeMessage]
    })
  },

  // 处理输入
  onInput(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  // 发送消息
  sendMessage() {
    const { inputText } = this.data
    if (!inputText.trim()) return

    this.addUserMessage(inputText)
    this.setData({ inputText: '' })
    this.processMessage(inputText)
  },

  // 快速提问
  quickQuestion(e) {
    const { question } = e.currentTarget.dataset
    this.addUserMessage(question)
    this.processMessage(question)
  },

  // 添加用户消息
  addUserMessage(content) {
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: content,
      timestamp: new Date().toISOString()
    }

    this.setData({
      messages: [...this.data.messages, userMessage]
    })

    // 记录用户提问
    analyticsUtils.trackEvent('chat_question', {
      question: content,
      timestamp: new Date().toISOString()
    })

    this.scrollToBottom()
  },

  // 添加机器人消息
  addBotMessage(content, relatedQuestions = []) {
    this.setData({ isTyping: true })

    // 模拟打字效果
    setTimeout(() => {
      const botMessage = {
        id: Date.now(),
        type: 'bot',
        content: content,
        relatedQuestions: relatedQuestions,
        timestamp: new Date().toISOString()
      }

      this.setData({
        messages: [...this.data.messages, botMessage],
        isTyping: false
      })

      this.scrollToBottom()
    }, 1000)
  },

  // 处理消息
  processMessage(message) {
    const { qaDatabase } = this.data
    
    // 简单的关键词匹配
    let bestMatch = null
    let maxScore = 0

    Object.keys(qaDatabase).forEach(question => {
      const score = this.calculateSimilarity(message, question)
      if (score > maxScore) {
        maxScore = score
        bestMatch = question
      }
    })

    if (maxScore > 0.3 && bestMatch) {
      const qa = qaDatabase[bestMatch]
      this.addBotMessage(qa.answer, qa.relatedQuestions)
      
      // 记录匹配成功
      analyticsUtils.trackEvent('chat_match_success', {
        userQuestion: message,
        matchedQuestion: bestMatch,
        score: maxScore
      })
    } else {
      // 没有匹配到合适的答案
      const defaultResponse = '抱歉，我暂时无法回答这个问题。\n\n您可以：\n• 尝试重新描述问题\n• 选择下方的常见问题\n• 拨打招生咨询电话：020-39322681\n• 访问学校官网：www.gdut.edu.cn'
      
      this.addBotMessage(defaultResponse, ['学校有哪些特色专业', '录取分数线是多少'])
      
      // 记录匹配失败
      analyticsUtils.trackEvent('chat_match_failed', {
        userQuestion: message,
        timestamp: new Date().toISOString()
      })
    }
  },

  // 计算文本相似度（简单实现）
  calculateSimilarity(text1, text2) {
    const words1 = text1.replace(/[？?！!。.，,]/g, '').split('')
    const words2 = text2.replace(/[？?！!。.，,]/g, '').split('')
    
    let matchCount = 0
    words1.forEach(word => {
      if (words2.includes(word)) {
        matchCount++
      }
    })
    
    return matchCount / Math.max(words1.length, words2.length)
  },

  // 相关问题点击
  relatedQuestionTap(e) {
    const { question } = e.currentTarget.dataset
    this.addUserMessage(question)
    this.processMessage(question)
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      wx.pageScrollTo({
        scrollTop: 999999,
        duration: 300
      })
    }, 100)
  },

  // 清空聊天记录
  clearChat() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.initChat()
          analyticsUtils.trackEvent('chat_clear', {
            messageCount: this.data.messages.length
          })
        }
      }
    })
  },

  // 联系人工客服
  contactService() {
    wx.showModal({
      title: '人工客服',
      content: '如需人工咨询，请拨打招生咨询热线：\n\n📞 020-39322681\n\n⏰ 工作时间：\n周一至周五 8:30-17:30\n\n或发送邮件至：\<EMAIL>',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '020-39322681'
          })
        }
      }
    })

    analyticsUtils.trackEvent('contact_service', {
      source: 'chat',
      timestamp: new Date().toISOString()
    })
  },

  // 分享功能
  onShareAppMessage() {
    analyticsUtils.trackEvent('page_share', {
      page: 'chat',
      method: 'app_message'
    })

    return {
      title: '广东工业大学招生咨询',
      desc: '智能招生咨询助手，为您解答招生相关问题',
      path: '/pages/chat/chat'
    }
  }
})
