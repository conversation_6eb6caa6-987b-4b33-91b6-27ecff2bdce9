/* pages/chat/chat.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 0;
}

.message-item {
  margin-bottom: 30rpx;
}

/* 机器人消息 */
.bot-message {
  display: flex;
  align-items: flex-start;
}

.bot-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.bot-message .message-content {
  flex: 1;
  max-width: 80%;
}

.bot-bubble {
  background: white;
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  padding: 20rpx 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 用户消息 */
.user-message {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.user-message .message-content {
  flex: 1;
  max-width: 80%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-bubble {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  color: white;
  border-radius: 20rpx 20rpx 8rpx 20rpx;
  padding: 20rpx 25rpx;
}

/* 消息文本 */
.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 相关问题 */
.related-questions {
  margin-top: 20rpx;
}

.related-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.question-tag {
  background: #E3F2FD;
  color: #1E88E5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: 1rpx solid #BBDEFB;
  transition: all 0.3s;
}

.question-tag:active {
  background: #BBDEFB;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.typing-content {
  flex: 1;
  max-width: 80%;
}

.typing-bubble {
  background: white;
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  padding: 20rpx 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.typing-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ccc;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 快速问题 */
.quick-questions {
  background: white;
  padding: 20rpx;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.quick-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.questions-scroll {
  white-space: nowrap;
}

.question-item {
  display: inline-block;
  background: #E3F2FD;
  color: #1E88E5;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  margin-right: 15rpx;
  border: 1rpx solid #BBDEFB;
  transition: all 0.3s;
}

.question-item:active {
  background: #BBDEFB;
}

/* 输入区域 */
.input-section {
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.message-input {
  flex: 1;
  height: 70rpx;
  background: #f8f9fa;
  border-radius: 35rpx;
  padding: 0 25rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f9fa;
  transition: border-color 0.3s;
}

.message-input:focus {
  border-color: #1E88E5;
}

.send-btn {
  width: 120rpx;
  height: 70rpx;
  background: #ddd;
  color: white;
  border-radius: 35rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s;
}

.send-btn.active {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
}

.send-btn::after {
  border: none;
}

/* 功能按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 15rpx;
  transition: background-color 0.3s;
}

.action-btn:active {
  background-color: #f0f0f0;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 22rpx;
  color: #666;
}
