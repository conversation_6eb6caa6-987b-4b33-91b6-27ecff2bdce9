<!--pages/chat/chat.wxml-->
<view class="container">
  <!-- 聊天消息列表 -->
  <scroll-view class="messages-container" scroll-y="true" scroll-top="{{scrollTop}}" scroll-with-animation="true">
    <view class="message-item {{item.type}}" wx:for="{{messages}}" wx:key="id">
      <!-- 机器人消息 -->
      <view class="bot-message" wx:if="{{item.type === 'bot'}}">
        <view class="avatar bot-avatar">🤖</view>
        <view class="message-content">
          <view class="message-bubble bot-bubble">
            <text class="message-text">{{item.content}}</text>
          </view>
          <view class="message-time">{{item.timestamp}}</view>
          
          <!-- 相关问题 -->
          <view class="related-questions" wx:if="{{item.relatedQuestions && item.relatedQuestions.length > 0}}">
            <view class="related-title">您可能还想了解：</view>
            <view class="question-tags">
              <view class="question-tag" wx:for="{{item.relatedQuestions}}" wx:key="index" wx:for-item="question" bindtap="relatedQuestionTap" data-question="{{question}}">
                {{question}}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 用户消息 -->
      <view class="user-message" wx:if="{{item.type === 'user'}}">
        <view class="message-content">
          <view class="message-bubble user-bubble">
            <text class="message-text">{{item.content}}</text>
          </view>
          <view class="message-time">{{item.timestamp}}</view>
        </view>
        <view class="avatar user-avatar">👤</view>
      </view>
    </view>

    <!-- 打字指示器 -->
    <view class="typing-indicator" wx:if="{{isTyping}}">
      <view class="avatar bot-avatar">🤖</view>
      <view class="typing-content">
        <view class="typing-bubble">
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 快速问题 -->
  <view class="quick-questions" wx:if="{{messages.length <= 1}}">
    <view class="quick-title">常见问题：</view>
    <scroll-view class="questions-scroll" scroll-x="true">
      <view class="question-item" wx:for="{{quickQuestions}}" wx:key="index" bindtap="quickQuestion" data-question="{{item}}">
        {{item}}
      </view>
    </scroll-view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-container">
      <input class="message-input" 
             placeholder="请输入您的问题..." 
             value="{{inputText}}" 
             bindinput="onInput" 
             confirm-type="send" 
             bindconfirm="sendMessage" />
      <button class="send-btn {{inputText ? 'active' : ''}}" bindtap="sendMessage" disabled="{{!inputText}}">
        发送
      </button>
    </view>
    
    <!-- 功能按钮 -->
    <view class="action-buttons">
      <view class="action-btn" bindtap="clearChat">
        <text class="btn-icon">🗑️</text>
        <text class="btn-text">清空</text>
      </view>
      <view class="action-btn" bindtap="contactService">
        <text class="btn-icon">👨‍💼</text>
        <text class="btn-text">人工客服</text>
      </view>
    </view>
  </view>
</view>
