// pages/system-status/system-status.js
const { performanceUtils, errorUtils, cacheUtils } = require('../../utils/performance-util')
const { favoriteUtils, historyUtils, analyticsUtils } = require('../../utils/favorite-util')
const { shareUtils } = require('../../utils/share-util')

Page({
  data: {
    systemInfo: {},
    performanceReport: {},
    errorStats: {},
    cacheStats: {},
    userStats: {},
    shareStats: {},
    storageInfo: {},
    activeTab: 'overview',
    tabs: [
      { id: 'overview', name: '概览', icon: '📊' },
      { id: 'performance', name: '性能', icon: '⚡' },
      { id: 'errors', name: '错误', icon: '🚨' },
      { id: 'storage', name: '存储', icon: '💾' }
    ]
  },

  onLoad() {
    this.loadSystemStatus()
  },

  onShow() {
    this.loadSystemStatus()
  },

  // 加载系统状态
  loadSystemStatus() {
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync()
      
      // 获取性能报告
      const performanceReport = performanceUtils.getPerformanceReport()
      
      // 获取错误统计
      const errorStats = errorUtils.getErrorStats()
      
      // 获取缓存统计
      const cacheStats = cacheUtils.getCacheStats()
      
      // 获取用户行为统计
      const userStats = this.getUserStats()
      
      // 获取分享统计
      const shareStats = shareUtils.getShareStats()
      
      // 获取存储信息
      const storageInfo = wx.getStorageInfoSync()

      this.setData({
        systemInfo: systemInfo,
        performanceReport: performanceReport,
        errorStats: errorStats,
        cacheStats: cacheStats,
        userStats: userStats,
        shareStats: shareStats,
        storageInfo: storageInfo
      })
    } catch (error) {
      console.error('加载系统状态失败:', error)
      errorUtils.logError(error, { context: 'loadSystemStatus' })
    }
  },

  // 获取用户统计
  getUserStats() {
    try {
      const favoriteStats = favoriteUtils.getFavoriteStats()
      const historyStats = historyUtils.getHistoryStats()
      const eventStats = analyticsUtils.getEventStats(7)
      
      return {
        favorites: favoriteStats,
        history: historyStats,
        events: eventStats
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return { favorites: {}, history: {}, events: {} }
    }
  },

  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  // 清理缓存
  clearCache() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有缓存数据吗？这可能会影响应用性能。',
      success: (res) => {
        if (res.confirm) {
          const success = cacheUtils.clearAllCache()
          if (success) {
            wx.showToast({
              title: '缓存已清理',
              icon: 'success'
            })
            this.loadSystemStatus()
          } else {
            wx.showToast({
              title: '清理失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 清理错误日志
  clearErrorLogs() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有错误日志吗？',
      success: (res) => {
        if (res.confirm) {
          errorUtils.clearErrorLogs()
          wx.showToast({
            title: '错误日志已清理',
            icon: 'success'
          })
          this.loadSystemStatus()
        }
      }
    })
  },

  // 清理性能数据
  clearPerformanceData() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有性能监控数据吗？',
      success: (res) => {
        if (res.confirm) {
          performanceUtils.clearPerformanceData()
          wx.showToast({
            title: '性能数据已清理',
            icon: 'success'
          })
          this.loadSystemStatus()
        }
      }
    })
  },

  // 清理分享统计
  clearShareStats() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理分享统计数据吗？',
      success: (res) => {
        if (res.confirm) {
          shareUtils.cleanupShareStats()
          wx.showToast({
            title: '分享统计已清理',
            icon: 'success'
          })
          this.loadSystemStatus()
        }
      }
    })
  },

  // 导出系统报告
  exportSystemReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        systemInfo: this.data.systemInfo,
        performance: this.data.performanceReport,
        errors: this.data.errorStats,
        cache: this.data.cacheStats,
        user: this.data.userStats,
        share: this.data.shareStats,
        storage: this.data.storageInfo
      }

      const reportStr = JSON.stringify(report, null, 2)
      
      wx.setClipboardData({
        data: reportStr,
        success: () => {
          wx.showToast({
            title: '报告已复制到剪贴板',
            icon: 'success',
            duration: 2000
          })
        }
      })
    } catch (error) {
      console.error('导出系统报告失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  // 查看错误详情
  viewErrorDetail(e) {
    const { error } = e.currentTarget.dataset
    const content = `错误类型：${error.type}\n页面：${error.page}\n时间：${error.timestamp}\n消息：${error.message}`
    
    wx.showModal({
      title: '错误详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 格式化存储大小
  formatStorageSize(size) {
    if (size < 1024) {
      return size + ' B'
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(1) + ' KB'
    } else {
      return (size / (1024 * 1024)).toFixed(1) + ' MB'
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp)
    return date.toLocaleString()
  },

  // 分享系统状态
  onShareAppMessage() {
    return {
      title: '广东工业大学招生小程序 - 系统状态',
      desc: '查看应用性能和使用统计',
      path: '/pages/system-status/system-status'
    }
  }
})
