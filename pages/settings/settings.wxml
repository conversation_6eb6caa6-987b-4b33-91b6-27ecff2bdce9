<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 学校信息卡片 -->
  <view class="school-info-section">
    <view class="school-info-card">
      <view class="school-name">{{schoolInfo.name}}</view>
      <view class="school-website">{{schoolInfo.website}}</view>
      <view class="contact-info">
        <view class="contact-item">
          <text class="contact-icon">📞</text>
          <text class="contact-text">{{schoolInfo.phone}}</text>
        </view>
        <view class="contact-item">
          <text class="contact-icon">📧</text>
          <text class="contact-text">{{schoolInfo.email}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-section">
    <view class="section-title">快捷操作</view>
    <view class="actions-grid">
      <view class="action-item" wx:for="{{quickActions}}" wx:key="id" bindtap="handleQuickAction" data-action="{{item}}" style="background-color: {{item.color}};">
        <view class="action-icon">{{item.icon}}</view>
        <view class="action-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 设置列表 -->
  <view class="settings-section">
    <view class="section-title">设置</view>
    <view class="settings-list">
      <view class="setting-item" wx:for="{{settings}}" wx:key="id" bindtap="handleSettingTap" data-setting="{{item}}">
        <view class="setting-icon">{{item.icon}}</view>
        <view class="setting-info">
          <view class="setting-name">{{item.name}}</view>
          <view class="setting-desc">{{item.desc}}</view>
        </view>
        <view class="setting-arrow" wx:if="{{item.showArrow}}">→</view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-section">
    <view class="version-text">版本 {{version}}</view>
    <view class="copyright-text">© 2024 广东工业大学</view>
  </view>
</view>
