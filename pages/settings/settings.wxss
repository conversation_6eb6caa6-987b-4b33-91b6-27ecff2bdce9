/* pages/settings/settings.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 学校信息卡片 */
.school-info-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.school-info-card {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  text-align: center;
  box-shadow: 0 8rpx 30rpx rgba(30, 136, 229, 0.3);
}

.school-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.school-website {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 25rpx;
}

.contact-info {
  display: flex;
  justify-content: space-around;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 22rpx;
}

.contact-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.contact-text {
  opacity: 0.9;
}

/* 快捷操作 */
.quick-actions-section {
  padding: 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  color: white;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 50rpx;
  margin-bottom: 15rpx;
}

.action-name {
  font-size: 26rpx;
  font-weight: bold;
}

/* 设置列表 */
.settings-section {
  padding: 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.settings-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9fa;
}

.setting-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.setting-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.setting-arrow {
  font-size: 28rpx;
  color: #ccc;
  margin-left: 15rpx;
}

/* 版本信息 */
.version-section {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 40rpx;
}

.version-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.copyright-text {
  font-size: 22rpx;
  color: #999;
}
