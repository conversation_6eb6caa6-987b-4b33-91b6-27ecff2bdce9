// pages/settings/settings.js
Page({
  data: {
    version: '1.0.0',
    schoolInfo: {
      name: '广东工业大学',
      website: 'www.gdut.edu.cn',
      phone: '020-39322681',
      email: '<EMAIL>'
    },
    settings: [
      {
        id: 'contact',
        name: '联系我们',
        icon: '📞',
        desc: '招生咨询联系方式',
        showArrow: true
      },
      {
        id: 'website',
        name: '官方网站',
        icon: '🌐',
        desc: '访问学校官方网站',
        showArrow: true
      },
      {
        id: 'feedback',
        name: '意见反馈',
        icon: '💬',
        desc: '提出宝贵意见建议',
        showArrow: true
      },
      {
        id: 'about',
        name: '关于小程序',
        icon: 'ℹ️',
        desc: '版本信息与帮助',
        showArrow: true
      }
    ],
    quickActions: [
      {
        id: 'admission',
        name: '录取查询',
        icon: '🔍',
        color: '#9C27B0'
      },
      {
        id: 'scores',
        name: '分数线',
        icon: '📊',
        color: '#2196F3'
      },
      {
        id: 'majors',
        name: '专业查询',
        icon: '📚',
        color: '#4CAF50'
      },
      {
        id: 'campus',
        name: '校园导览',
        icon: '🏫',
        color: '#FF9800'
      }
    ]
  },

  onLoad() {
    // 页面加载
  },

  // 处理设置项点击
  handleSettingTap(e) {
    const { setting } = e.currentTarget.dataset

    switch(setting.id) {
      case 'contact':
        this.showContact()
        break
      case 'website':
        this.openWebsite()
        break
      case 'feedback':
        this.showFeedback()
        break
      case 'about':
        this.showAbout()
        break
    }
  },

  // 快捷操作
  handleQuickAction(e) {
    const { action } = e.currentTarget.dataset

    switch(action.id) {
      case 'admission':
        wx.switchTab({
          url: '/pages/admission/admission'
        })
        break
      case 'scores':
        wx.switchTab({
          url: '/pages/admission/admission'
        })
        break
      case 'majors':
        wx.switchTab({
          url: '/pages/majors/majors'
        })
        break
      case 'campus':
        wx.switchTab({
          url: '/pages/campus/campus'
        })
        break
    }
  },

  // 显示联系方式
  showContact() {
    const { schoolInfo } = this.data
    wx.showModal({
      title: '联系我们',
      content: `招生咨询热线：${schoolInfo.phone}\n\n招生咨询邮箱：${schoolInfo.email}\n\n官方网站：${schoolInfo.website}\n\n地址：广州市番禺区大学城外环西路100号`,
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: schoolInfo.phone
          })
        }
      }
    })
  },

  // 打开官网
  openWebsite() {
    wx.showModal({
      title: '访问官网',
      content: '即将跳转到广东工业大学官方网站，是否继续？',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'https://www.gdut.edu.cn',
            success: () => {
              wx.showToast({
                title: '网址已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 意见反馈
  showFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您使用广东工业大学招生小程序！\n\n如有任何意见或建议，请通过以下方式联系我们：\n\n1. 招生咨询电话\n2. 官方邮箱\n3. 学校官网留言',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 显示关于信息
  showAbout() {
    wx.showModal({
      title: '关于小程序',
      content: `广东工业大学招生小程序\n版本：${this.data.version}\n\n🎓 主要功能：\n• 校园介绍\n• 专业查询\n• 师资介绍\n• 录取自查\n• 招生政策\n\n为广大考生提供便捷的招生信息查询服务`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '广东工业大学招生小程序',
      path: '/pages/index/index',
      imageUrl: '/images/share/share.jpg'
    }
  },

  onShareTimeline() {
    return {
      title: '广东工业大学 - 你的理想大学在这里',
      imageUrl: '/images/share/share.jpg'
    }
  }
})
