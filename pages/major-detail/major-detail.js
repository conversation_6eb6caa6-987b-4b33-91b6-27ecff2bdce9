// pages/major-detail/major-detail.js
Page({
  data: {
    majorId: null,
    major: null,
    activeTab: 'overview',
    tabs: [
      { id: 'overview', name: '专业概况' },
      { id: 'curriculum', name: '课程设置' },
      { id: 'employment', name: '就业前景' },
      { id: 'admission', name: '招生信息' }
    ],
    // 模拟详细数据
    majorDetails: {
      1: {
        id: 1,
        name: '机械设计制造及其自动化',
        college: '机电工程学院',
        overview: {
          description: '机械设计制造及其自动化专业是以机械设计与制造为基础，融入计算机科学、信息技术、自动控制技术的交叉学科，主要任务是运用先进设计制造技术的理论与方法，解决现代工程领域中的复杂技术问题，以实现产品智能化的设计与制造。',
          objectives: [
            '培养具备机械设计制造基础知识与应用能力',
            '能在工业生产第一线从事机械制造领域内的设计制造',
            '科技开发、应用研究、运行管理和经营销售等方面工作',
            '具有创新精神和实践能力的高级工程技术人才'
          ],
          features: [
            '国家特色专业建设点',
            '通过工程教育专业认证',
            '拥有完善的实验实训设备',
            '与多家知名企业建立合作关系'
          ]
        },
        curriculum: {
          core: [
            '机械制图', '理论力学', '材料力学', '机械原理',
            '机械设计', '机械制造技术基础', '电工与电子技术',
            '微机原理及应用', '机械工程控制基础', '数控技术'
          ],
          practical: [
            '金工实习', '机械设计课程设计', '机械制造技术基础课程设计',
            '专业实习', '毕业设计', '创新创业实践'
          ],
          elective: [
            '先进制造技术', '机器人技术', '3D打印技术',
            '工业4.0概论', '人工智能在制造业的应用'
          ]
        },
        employment: {
          rate: '95%',
          salary: '8000-12000元',
          directions: [
            {
              name: '机械设计工程师',
              description: '从事机械产品设计、开发和改进工作',
              companies: ['华为', '比亚迪', '格力电器', '美的集团'],
              salary: '8000-15000元'
            },
            {
              name: '制造工程师',
              description: '负责生产工艺设计和制造过程优化',
              companies: ['富士康', '立讯精密', '欣旺达', '德赛电池'],
              salary: '7000-12000元'
            },
            {
              name: '技术研发工程师',
              description: '从事新产品研发和技术创新工作',
              companies: ['中兴通讯', '大疆创新', '汇川技术', '易事特'],
              salary: '10000-18000元'
            }
          ],
          industries: [
            '汽车制造业', '电子信息产业', '装备制造业',
            '新能源产业', '航空航天', '机器人产业'
          ]
        },
        admission: {
          duration: '4年',
          degree: '工学学士',
          enrollment: '120人',
          requirements: [
            '高考成绩达到本科一批录取分数线',
            '数学、物理成绩优秀',
            '具备良好的空间想象能力',
            '对机械制造有浓厚兴趣'
          ],
          scores: {
            '2023': { min: 580, avg: 595, max: 620 },
            '2022': { min: 575, avg: 590, max: 615 },
            '2021': { min: 570, avg: 585, max: 610 }
          }
        }
      }
      // 可以添加更多专业的详细信息
    }
  },

  onLoad(options) {
    const majorId = parseInt(options.id)
    this.setData({
      majorId: majorId
    })
    this.loadMajorDetail(majorId)
  },

  loadMajorDetail(majorId) {
    // 从本地数据加载专业详情
    const majorDetail = this.data.majorDetails[majorId]
    if (majorDetail) {
      this.setData({
        major: majorDetail
      })
    } else {
      // 如果没有详细数据，显示基本信息
      wx.showToast({
        title: '专业详情加载中...',
        icon: 'loading'
      })
    }
  },

  // 切换标签页
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  // 查看就业详情
  viewEmploymentDetail(e) {
    const { direction } = e.currentTarget.dataset
    const content = `职位描述：${direction.description}\n\n主要企业：${direction.companies.join('、')}\n\n薪资范围：${direction.salary}`
    
    wx.showModal({
      title: direction.name,
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看历年分数
  viewScoreDetail() {
    const { scores } = this.data.major.admission
    let content = '历年录取分数线：\n\n'
    
    Object.keys(scores).forEach(year => {
      const score = scores[year]
      content += `${year}年：最低${score.min}分，平均${score.avg}分，最高${score.max}分\n`
    })
    
    wx.showModal({
      title: '历年录取分数',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    const { major } = this.data
    return {
      title: `${major.name} - 专业详情`,
      path: `/pages/major-detail/major-detail?id=${major.id}`
    }
  }
})
