/* pages/major-detail/major-detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部信息 */
.header-section {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  color: white;
  padding: 40rpx 30rpx;
  text-align: center;
}

.major-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.major-college {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 标签页导航 */
.tabs-section {
  background: white;
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  color: #1E88E5;
  border-bottom-color: #1E88E5;
  font-weight: bold;
}

/* 内容区域 */
.content-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #1E88E5;
}

/* 专业概况样式 */
.overview-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.description {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  margin-bottom: 30rpx;
  text-align: justify;
}

.objectives-list {
  margin-bottom: 30rpx;
}

.objective-item {
  display: flex;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  line-height: 1.5;
}

.bullet {
  color: #1E88E5;
  margin-right: 15rpx;
  font-weight: bold;
}

.objective-text {
  flex: 1;
  color: #666;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.feature-item {
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 15rpx;
  text-align: center;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 课程设置样式 */
.curriculum-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.course-category {
  margin-bottom: 40rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #E3F2FD;
}

.course-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.course-item {
  background: #E3F2FD;
  color: #1E88E5;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  border: 2rpx solid #E3F2FD;
}

.course-item.practical {
  background: #E8F5E8;
  color: #4CAF50;
  border-color: #E8F5E8;
}

.course-item.elective {
  background: #FFF3E0;
  color: #FF9800;
  border-color: #FFF3E0;
}

/* 就业前景样式 */
.employment-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.employment-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #E3F2FD, #F3E5F5);
  border-radius: 15rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.employment-directions {
  margin-bottom: 30rpx;
}

.direction-card {
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 15rpx;
  margin-bottom: 15rpx;
  border-left: 6rpx solid #1E88E5;
}

.direction-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.direction-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.direction-salary {
  font-size: 26rpx;
  color: #FF9800;
  font-weight: bold;
}

.industries-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.industry-item {
  background: #E8F5E8;
  color: #4CAF50;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
}

/* 招生信息样式 */
.admission-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.admission-info {
  margin-bottom: 30rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.info-item {
  text-align: center;
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.info-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.info-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1E88E5;
}

.requirements-list {
  margin-bottom: 30rpx;
}

.requirement-item {
  display: flex;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  line-height: 1.5;
}

.requirement-text {
  flex: 1;
  color: #666;
}

.score-card {
  background: linear-gradient(135deg, #E3F2FD, #F3E5F5);
  padding: 30rpx;
  border-radius: 15rpx;
  text-align: center;
}

.score-tip {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.score-preview {
  font-size: 28rpx;
  color: #1E88E5;
  font-weight: bold;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
