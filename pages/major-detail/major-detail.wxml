<!--pages/major-detail/major-detail.wxml-->
<view class="container" wx:if="{{major}}">
  <!-- 专业头部信息 -->
  <view class="header-section">
    <view class="major-name">{{major.name}}</view>
    <view class="major-college">{{major.college}}</view>
  </view>

  <!-- 标签页导航 -->
  <view class="tabs-section">
    <view class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
          wx:for="{{tabs}}" 
          wx:key="id" 
          bindtap="switchTab" 
          data-tab="{{item.id}}">
      {{item.name}}
    </view>
  </view>

  <!-- 专业概况 -->
  <view class="content-section" wx:if="{{activeTab === 'overview'}}">
    <view class="overview-content">
      <view class="section-title">专业介绍</view>
      <view class="description">{{major.overview.description}}</view>
      
      <view class="section-title">培养目标</view>
      <view class="objectives-list">
        <view class="objective-item" wx:for="{{major.overview.objectives}}" wx:key="index">
          <text class="bullet">•</text>
          <text class="objective-text">{{item}}</text>
        </view>
      </view>
      
      <view class="section-title">专业特色</view>
      <view class="features-grid">
        <view class="feature-item" wx:for="{{major.overview.features}}" wx:key="index">
          <view class="feature-icon">✨</view>
          <view class="feature-text">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 课程设置 -->
  <view class="content-section" wx:if="{{activeTab === 'curriculum'}}">
    <view class="curriculum-content">
      <view class="course-category">
        <view class="category-title">核心课程</view>
        <view class="course-grid">
          <view class="course-item" wx:for="{{major.curriculum.core}}" wx:key="index">{{item}}</view>
        </view>
      </view>
      
      <view class="course-category">
        <view class="category-title">实践环节</view>
        <view class="course-grid">
          <view class="course-item practical" wx:for="{{major.curriculum.practical}}" wx:key="index">{{item}}</view>
        </view>
      </view>
      
      <view class="course-category">
        <view class="category-title">选修课程</view>
        <view class="course-grid">
          <view class="course-item elective" wx:for="{{major.curriculum.elective}}" wx:key="index">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 就业前景 -->
  <view class="content-section" wx:if="{{activeTab === 'employment'}}">
    <view class="employment-content">
      <view class="employment-stats">
        <view class="stat-item">
          <view class="stat-value">{{major.employment.rate}}</view>
          <view class="stat-label">就业率</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{major.employment.salary}}</view>
          <view class="stat-label">薪资范围</view>
        </view>
      </view>
      
      <view class="section-title">就业方向</view>
      <view class="employment-directions">
        <view class="direction-card" wx:for="{{major.employment.directions}}" wx:key="name" bindtap="viewEmploymentDetail" data-direction="{{item}}">
          <view class="direction-name">{{item.name}}</view>
          <view class="direction-desc">{{item.description}}</view>
          <view class="direction-salary">{{item.salary}}</view>
        </view>
      </view>
      
      <view class="section-title">就业行业</view>
      <view class="industries-grid">
        <view class="industry-item" wx:for="{{major.employment.industries}}" wx:key="index">{{item}}</view>
      </view>
    </view>
  </view>

  <!-- 招生信息 -->
  <view class="content-section" wx:if="{{activeTab === 'admission'}}">
    <view class="admission-content">
      <view class="admission-info">
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">学制</view>
            <view class="info-value">{{major.admission.duration}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">学位</view>
            <view class="info-value">{{major.admission.degree}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">招生人数</view>
            <view class="info-value">{{major.admission.enrollment}}</view>
          </view>
        </view>
      </view>
      
      <view class="section-title">报考要求</view>
      <view class="requirements-list">
        <view class="requirement-item" wx:for="{{major.admission.requirements}}" wx:key="index">
          <text class="bullet">•</text>
          <text class="requirement-text">{{item}}</text>
        </view>
      </view>
      
      <view class="section-title">历年分数线</view>
      <view class="score-card" bindtap="viewScoreDetail">
        <view class="score-tip">点击查看详细分数信息</view>
        <view class="score-preview">
          <text>2023年平均分：595分</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-state" wx:if="{{!major}}">
  <view class="loading-icon">📚</view>
  <view class="loading-text">加载中...</view>
</view>
