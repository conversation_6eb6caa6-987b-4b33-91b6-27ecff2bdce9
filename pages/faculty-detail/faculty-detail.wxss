/* pages/faculty-detail/faculty-detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部信息 */
.header-section {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
}

.faculty-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.faculty-info {
  flex: 1;
}

.faculty-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.faculty-title {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.faculty-college {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 6rpx;
}

.faculty-speciality {
  font-size: 22rpx;
  opacity: 0.7;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.star-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-icon {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.7);
}

.star-icon.starred {
  color: #FFD700;
}

/* 标签页导航 */
.tabs-section {
  background: white;
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  color: #FF9800;
  border-bottom-color: #FF9800;
  font-weight: bold;
}

/* 内容区域 */
.content-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #FF9800;
}

/* 个人简介样式 */
.bio-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.bio-text, .research-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  margin-bottom: 30rpx;
  text-align: justify;
}

.college-info {
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 15rpx;
}

.college-name {
  font-size: 28rpx;
  color: #FF9800;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.speciality-name {
  font-size: 24rpx;
  color: #666;
}

/* 研究方向样式 */
.research-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.research-detail {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  margin-bottom: 30rpx;
  text-align: justify;
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.project-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.project-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.project-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}

/* 成就样式 */
.achievements-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  background: linear-gradient(135deg, #FFF3E0, #FFECB3);
  border-radius: 15rpx;
  border-left: 6rpx solid #FF9800;
}

.achievement-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.achievement-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

/* 作品样式 */
.publications-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.publications-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.publication-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  border-left: 6rpx solid #2196F3;
}

.publication-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  color: #2196F3;
}

.publication-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

/* 联系方式 */
.contact-section {
  padding: 30rpx;
}

.contact-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(255, 152, 0, 0.3);
}

.contact-btn::after {
  border: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
