<!--pages/faculty-detail/faculty-detail.wxml-->
<view class="container" wx:if="{{faculty}}">
  <!-- 教师头部信息 -->
  <view class="header-section">
    <image class="faculty-avatar" src="{{faculty.avatar}}" mode="aspectFill" />
    <view class="faculty-info">
      <view class="faculty-name">{{faculty.name}}</view>
      <view class="faculty-title">{{faculty.title}}</view>
      <view class="faculty-college">{{faculty.college}}</view>
      <view class="faculty-speciality">{{faculty.speciality}}</view>
    </view>
    <view class="action-buttons">
      <view class="star-btn" bindtap="toggleStar">
        <text class="star-icon {{faculty.isStarred ? 'starred' : ''}}">{{faculty.isStarred ? '★' : '☆'}}</text>
      </view>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tabs-section">
    <view class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
          wx:for="{{tabs}}" 
          wx:key="id" 
          bindtap="switchTab" 
          data-tab="{{item.id}}">
      {{item.name}}
    </view>
  </view>

  <!-- 个人简介 -->
  <view class="content-section" wx:if="{{activeTab === 'bio'}}">
    <view class="bio-content">
      <view class="section-title">个人简介</view>
      <view class="bio-text">{{faculty.bio || '暂无个人简介信息'}}</view>
      
      <view class="section-title">研究领域</view>
      <view class="research-text">{{faculty.research}}</view>
      
      <view class="section-title">所属学院</view>
      <view class="college-info">
        <view class="college-name">{{faculty.college}}</view>
        <view class="speciality-name">{{faculty.speciality}}</view>
      </view>
    </view>
  </view>

  <!-- 研究方向 -->
  <view class="content-section" wx:if="{{activeTab === 'research'}}">
    <view class="research-content">
      <view class="section-title">主要研究方向</view>
      <view class="research-detail">{{faculty.research}}</view>
      
      <view class="section-title" wx:if="{{faculty.projects}}">主要项目</view>
      <view class="projects-list" wx:if="{{faculty.projects}}">
        <view class="project-item" wx:for="{{faculty.projects}}" wx:key="index">
          <view class="project-icon">🔬</view>
          <view class="project-name">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要成就 -->
  <view class="content-section" wx:if="{{activeTab === 'achievements'}}">
    <view class="achievements-content">
      <view class="section-title">荣誉与奖项</view>
      <view class="achievements-list">
        <view class="achievement-item" wx:for="{{faculty.achievements}}" wx:key="index" bindtap="viewAchievementDetail" data-achievement="{{item}}">
          <view class="achievement-icon">🏆</view>
          <view class="achievement-text">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 代表作品 -->
  <view class="content-section" wx:if="{{activeTab === 'publications'}}">
    <view class="publications-content">
      <view class="section-title">代表性论文/著作</view>
      <view class="publications-list" wx:if="{{faculty.publications}}">
        <view class="publication-item" wx:for="{{faculty.publications}}" wx:key="index" bindtap="viewPublicationDetail" data-publication="{{item}}">
          <view class="publication-icon">📚</view>
          <view class="publication-text">{{item}}</view>
        </view>
      </view>
      <view class="empty-state" wx:else>
        <view class="empty-icon">📖</view>
        <view class="empty-text">暂无代表作品信息</view>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <button class="contact-btn" bindtap="contactFaculty">联系教师</button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-state" wx:if="{{!faculty}}">
  <view class="loading-icon">👨‍🏫</view>
  <view class="loading-text">加载中...</view>
</view>
