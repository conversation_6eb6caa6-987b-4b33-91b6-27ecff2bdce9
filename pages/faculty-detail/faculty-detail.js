// pages/faculty-detail/faculty-detail.js
const { facultyUtils } = require('../../utils/admission-util')

Page({
  data: {
    facultyId: null,
    faculty: null,
    activeTab: 'bio',
    tabs: [
      { id: 'bio', name: '个人简介' },
      { id: 'research', name: '研究方向' },
      { id: 'achievements', name: '主要成就' },
      { id: 'publications', name: '代表作品' }
    ]
  },

  onLoad(options) {
    const facultyId = parseInt(options.id)
    this.setData({
      facultyId: facultyId
    })
    this.loadFacultyDetail(facultyId)
  },

  loadFacultyDetail(facultyId) {
    const faculty = facultyUtils.getFacultyById(facultyId)
    if (faculty) {
      this.setData({
        faculty: faculty
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: faculty.name
      })
    } else {
      wx.showToast({
        title: '教师信息不存在',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 切换标签页
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  // 切换收藏状态
  toggleStar() {
    const { faculty } = this.data
    const allFaculty = facultyUtils.getAllFaculty()
    const index = allFaculty.findIndex(f => f.id === faculty.id)
    
    if (index !== -1) {
      allFaculty[index].isStarred = !allFaculty[index].isStarred
      wx.setStorageSync('faculty', allFaculty)
      
      this.setData({
        'faculty.isStarred': allFaculty[index].isStarred
      })

      wx.showToast({
        title: allFaculty[index].isStarred ? '已收藏' : '已取消收藏',
        icon: 'success',
        duration: 1000
      })
    }
  },

  // 查看成就详情
  viewAchievementDetail(e) {
    const { achievement } = e.currentTarget.dataset
    wx.showModal({
      title: '主要成就',
      content: achievement,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看作品详情
  viewPublicationDetail(e) {
    const { publication } = e.currentTarget.dataset
    wx.showModal({
      title: '代表作品',
      content: publication,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 联系教师
  contactFaculty() {
    const { faculty } = this.data
    wx.showModal({
      title: '联系方式',
      content: `如需联系${faculty.name}教授，请通过以下方式：\n\n1. 学院办公室咨询\n2. 官方邮箱联系\n3. 学术会议交流`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    const { faculty } = this.data
    return {
      title: `${faculty.name} - ${faculty.title}`,
      path: `/pages/faculty-detail/faculty-detail?id=${faculty.id}`,
      imageUrl: faculty.avatar
    }
  },

  onShareTimeline() {
    const { faculty } = this.data
    return {
      title: `${faculty.name} - 广东工业大学优秀教师`,
      imageUrl: faculty.avatar
    }
  }
})
