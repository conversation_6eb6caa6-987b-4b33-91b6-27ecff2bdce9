/* pages/reward-detail/reward-detail.wxss */

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  background-color: #fafafa;
  box-sizing: border-box;
  line-height: 40rpx;
}

.form-input:focus {
  border-color: #ff9800;
  background-color: white;
}

.form-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  background-color: #fafafa;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.form-hint {
  margin-top: 15rpx;
  font-size: 26rpx;
  color: #666;
  padding: 20rpx;
  background-color: #fff3e0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9800;
}

/* 预览样式 */
.reward-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff9800;
}

.preview-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.preview-points {
  font-size: 28rpx;
  font-weight: bold;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  margin-top: 60rpx;
  gap: 20rpx;
}

.action-buttons button {
  flex: 1;
}
