// pages/index/index.js
Page({
  data: {
    bannerImages: [
      '/images/banner/banner1.jpg',
      '/images/banner/banner2.jpg',
      '/images/banner/banner3.jpg'
    ],
    currentBanner: 0,
    schoolInfo: {
      name: '广东工业大学',
      slogan: '团结、勤奋、求是、创新',
      established: '1958年',
      students: '50000+',
      faculty: '2800+',
      majors: '80+'
    },
    quickNavs: [
      {
        id: 'campus',
        name: '校园介绍',
        icon: '🏫',
        color: '#4CAF50',
        path: '/pages/campus/campus'
      },
      {
        id: 'majors',
        name: '专业介绍',
        icon: '📚',
        color: '#2196F3',
        path: '/pages/majors/majors'
      },
      {
        id: 'faculty',
        name: '师资介绍',
        icon: '👨‍🏫',
        color: '#FF9800',
        path: '/pages/faculty/faculty'
      },
      {
        id: 'admission',
        name: '录取自查',
        icon: '🎓',
        color: '#9C27B0',
        path: '/pages/admission/admission'
      }
    ],
    newsData: [
      {
        id: 1,
        title: '2024年招生简章发布',
        summary: '广东工业大学2024年本科招生简章正式发布，欢迎广大考生报考。',
        date: '2024-05-15',
        type: 'important'
      },
      {
        id: 2,
        title: '校园开放日活动通知',
        summary: '6月份将举办校园开放日活动，欢迎考生和家长参观校园。',
        date: '2024-05-10',
        type: 'activity'
      },
      {
        id: 3,
        title: '优秀毕业生就业喜报',
        summary: '2024届毕业生就业率达95%以上，多名学生进入知名企业工作。',
        date: '2024-05-08',
        type: 'news'
      }
    ],
    featuresData: [
      {
        icon: '🏆',
        title: '办学实力',
        desc: '省部共建高校，工科特色鲜明'
      },
      {
        icon: '🌟',
        title: '师资力量',
        desc: '院士领衔，名师荟萃'
      },
      {
        icon: '🚀',
        title: '就业前景',
        desc: '就业率95%+，薪资水平领先'
      },
      {
        icon: '🌍',
        title: '国际化',
        desc: '广泛的国际交流与合作'
      }
    ]
  },

  onLoad() {
    this.startBannerTimer()
  },

  onUnload() {
    this.clearBannerTimer()
  },

  // 轮播图自动切换
  startBannerTimer() {
    this.bannerTimer = setInterval(() => {
      const { currentBanner, bannerImages } = this.data
      const nextBanner = (currentBanner + 1) % bannerImages.length
      this.setData({
        currentBanner: nextBanner
      })
    }, 4000)
  },

  clearBannerTimer() {
    if (this.bannerTimer) {
      clearInterval(this.bannerTimer)
      this.bannerTimer = null
    }
  },

  // 轮播图切换
  onBannerChange(e) {
    this.setData({
      currentBanner: e.detail.current
    })
  },

  // 快捷导航
  handleQuickNav(e) {
    const { nav } = e.currentTarget.dataset
    wx.navigateTo({
      url: nav.path
    })
  },

  // 查看新闻详情
  viewNewsDetail(e) {
    const { news } = e.currentTarget.dataset
    wx.showModal({
      title: news.title,
      content: news.summary,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 联系我们
  contactUs() {
    wx.showModal({
      title: '联系方式',
      content: '招生咨询电话：020-39322681\n招生咨询QQ：800007410\n官方网站：www.gdut.edu.cn',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '广东工业大学招生小程序',
      path: '/pages/index/index',
      imageUrl: '/images/share/share.jpg'
    }
  },

  onShareTimeline() {
    return {
      title: '广东工业大学 - 你的理想大学在这里',
      imageUrl: '/images/share/share.jpg'
    }
  }
})
