<!--pages/index/index.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="4000" duration="500" bindchange="onBannerChange">
      <swiper-item wx:for="{{bannerImages}}" wx:key="index">
        <image class="banner-image" src="{{item}}" mode="aspectFill" />
      </swiper-item>
    </swiper>
  </view>

  <!-- 学校信息卡片 -->
  <view class="school-info-section">
    <view class="school-info-card">
      <view class="school-name">{{schoolInfo.name}}</view>
      <view class="school-slogan">{{schoolInfo.slogan}}</view>
      <view class="school-stats">
        <view class="stat-item">
          <view class="stat-number">{{schoolInfo.established}}</view>
          <view class="stat-label">建校时间</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{schoolInfo.students}}</view>
          <view class="stat-label">在校学生</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{schoolInfo.faculty}}</view>
          <view class="stat-label">教职工</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{schoolInfo.majors}}</view>
          <view class="stat-label">本科专业</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷导航 -->
  <view class="nav-section">
    <view class="nav-title">快捷导航</view>
    <view class="nav-grid">
      <view class="nav-item" wx:for="{{quickNavs}}" wx:key="id" bindtap="handleQuickNav" data-nav="{{item}}" style="background-color: {{item.color}};">
        <view class="nav-icon">{{item.icon}}</view>
        <view class="nav-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 学校特色 -->
  <view class="features-section">
    <view class="features-title">学校特色</view>
    <view class="features-grid">
      <view class="feature-item" wx:for="{{featuresData}}" wx:key="title">
        <view class="feature-icon">{{item.icon}}</view>
        <view class="feature-title">{{item.title}}</view>
        <view class="feature-desc">{{item.desc}}</view>
      </view>
    </view>
  </view>

  <!-- 最新动态 -->
  <view class="news-section">
    <view class="news-title">最新动态</view>
    <view class="news-list">
      <view class="news-item {{item.type}}" wx:for="{{newsData}}" wx:key="id" bindtap="viewNewsDetail" data-news="{{item}}">
        <view class="news-header">
          <view class="news-type-badge" wx:if="{{item.type === 'important'}}">重要</view>
          <view class="news-date">{{item.date}}</view>
        </view>
        <view class="news-title-text">{{item.title}}</view>
        <view class="news-summary">{{item.summary}}</view>
      </view>
    </view>
  </view>

  <!-- 联系我们 -->
  <view class="contact-section">
    <button class="contact-btn" bindtap="contactUs">联系我们</button>
  </view>
</view>
