/* pages/index/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图样式 */
.banner-section {
  width: 100%;
  height: 400rpx;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 学校信息卡片 */
.school-info-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.school-info-card {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  text-align: center;
  box-shadow: 0 8rpx 30rpx rgba(30, 136, 229, 0.3);
}

.school-name {
  font-size: 42rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.school-slogan {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
  font-style: italic;
}

.school-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 快捷导航 */
.nav-section {
  padding: 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.nav-item {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  color: white;
}

.nav-item:active {
  transform: scale(0.95);
}

.nav-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
}

.nav-name {
  font-size: 28rpx;
  font-weight: bold;
}

/* 学校特色 */
.features-section {
  padding: 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.features-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 50rpx;
  margin-bottom: 15rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 最新动态 */
.news-section {
  padding: 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.news-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  text-align: center;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.news-item {
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  border-left: 6rpx solid #1E88E5;
}

.news-item.important {
  border-left-color: #FF6B6B;
  background: linear-gradient(135deg, #FFF5F5, #FFFFFF);
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.news-type-badge {
  background: #FF6B6B;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.news-date {
  font-size: 22rpx;
  color: #999;
}

.news-title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.news-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 联系我们 */
.contact-section {
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.contact-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(30, 136, 229, 0.3);
}

.contact-btn::after {
  border: none;
}
