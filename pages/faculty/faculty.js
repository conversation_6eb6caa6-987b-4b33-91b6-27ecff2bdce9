// pages/faculty/faculty.js
Page({
  data: {
    searchKeyword: '',
    selectedCategory: 'all',
    categories: [
      { id: 'all', name: '全部' },
      { id: 'academician', name: '院士' },
      { id: 'professor', name: '教授' },
      { id: 'associate', name: '副教授' },
      { id: 'young', name: '青年才俊' }
    ],
    facultyStats: {
      total: 2800,
      professors: 420,
      associates: 680,
      doctors: 1200,
      academicians: 8
    },
    facultyList: [
      {
        id: 1,
        name: '陈新',
        title: '中国工程院院士',
        college: '机电工程学院',
        category: 'academician',
        speciality: '机械制造及其自动化',
        avatar: '/images/faculty/chenxin.jpg',
        achievements: ['国家科技进步一等奖', '何梁何利基金科学与技术进步奖'],
        research: '精密制造技术、智能制造系统',
        isStarred: true
      },
      {
        id: 2,
        name: '王建华',
        title: '教授、博士生导师',
        college: '计算机学院',
        category: 'professor',
        speciality: '人工智能',
        avatar: '/images/faculty/wangjianhua.jpg',
        achievements: ['国家杰出青年科学基金获得者', '教育部长江学者'],
        research: '机器学习、深度学习、计算机视觉',
        isStarred: true
      },
      {
        id: 3,
        name: '李明',
        title: '教授、博士生导师',
        college: '自动化学院',
        category: 'professor',
        speciality: '控制科学与工程',
        avatar: '/images/faculty/liming.jpg',
        achievements: ['国家优秀青年科学基金获得者', '广东省珠江学者'],
        research: '智能控制、机器人技术、工业自动化',
        isStarred: false
      },
      {
        id: 4,
        name: '张丽',
        title: '副教授、硕士生导师',
        college: '材料与能源学院',
        category: 'associate',
        speciality: '材料科学与工程',
        avatar: '/images/faculty/zhangli.jpg',
        achievements: ['广东省科技进步二等奖', '优秀青年教师奖'],
        research: '新能源材料、纳米材料、功能材料',
        isStarred: false
      },
      {
        id: 5,
        name: '刘强',
        title: '副教授、硕士生导师',
        college: '土木与交通工程学院',
        category: 'associate',
        speciality: '结构工程',
        avatar: '/images/faculty/liuqiang.jpg',
        achievements: ['广东省自然科学基金优秀青年项目', '校级教学名师'],
        research: '结构健康监测、智能建造、绿色建筑',
        isStarred: false
      },
      {
        id: 6,
        name: '赵敏',
        title: '教授、博士生导师',
        college: '管理学院',
        category: 'professor',
        speciality: '管理科学与工程',
        avatar: '/images/faculty/zhaomin.jpg',
        achievements: ['国家社科基金重大项目主持人', '管理学院院长'],
        research: '供应链管理、运营管理、大数据分析',
        isStarred: true
      },
      {
        id: 7,
        name: '孙伟',
        title: '青年教授',
        college: '计算机学院',
        category: 'young',
        speciality: '软件工程',
        avatar: '/images/faculty/sunwei.jpg',
        achievements: ['IEEE Fellow', '国际顶级会议最佳论文奖'],
        research: '软件工程、区块链技术、分布式系统',
        isStarred: false
      },
      {
        id: 8,
        name: '周华',
        title: '青年副教授',
        college: '自动化学院',
        category: 'young',
        speciality: '电气工程',
        avatar: '/images/faculty/zhouhua.jpg',
        achievements: ['广东省青年科技奖', '校级青年拔尖人才'],
        research: '电力系统、新能源发电、智能电网',
        isStarred: false
      }
    ],
    filteredFaculty: []
  },

  onLoad() {
    this.setData({
      filteredFaculty: this.data.facultyList
    })
  },

  // 搜索教师
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    this.filterFaculty()
  },

  // 选择分类
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      selectedCategory: category
    })
    this.filterFaculty()
  },

  // 过滤教师
  filterFaculty() {
    const { facultyList, searchKeyword, selectedCategory } = this.data
    let filtered = facultyList

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(faculty => faculty.category === selectedCategory)
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase()
      filtered = filtered.filter(faculty => 
        faculty.name.toLowerCase().includes(keyword) ||
        faculty.college.toLowerCase().includes(keyword) ||
        faculty.speciality.toLowerCase().includes(keyword) ||
        faculty.research.toLowerCase().includes(keyword)
      )
    }

    this.setData({
      filteredFaculty: filtered
    })
  },

  // 查看教师详情
  viewFacultyDetail(e) {
    const { faculty } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/faculty-detail/faculty-detail?id=${faculty.id}`
    })
  },

  // 切换收藏状态
  toggleStar(e) {
    e.stopPropagation()
    const { index } = e.currentTarget.dataset
    const facultyList = this.data.facultyList
    const filteredFaculty = this.data.filteredFaculty
    
    // 找到在原数组中的索引
    const originalIndex = facultyList.findIndex(f => f.id === filteredFaculty[index].id)
    
    facultyList[originalIndex].isStarred = !facultyList[originalIndex].isStarred
    filteredFaculty[index].isStarred = facultyList[originalIndex].isStarred
    
    this.setData({
      facultyList: facultyList,
      filteredFaculty: filteredFaculty
    })

    wx.showToast({
      title: filteredFaculty[index].isStarred ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000
    })
  },

  // 清空搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    })
    this.filterFaculty()
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '广东工业大学 - 师资介绍',
      path: '/pages/faculty/faculty'
    }
  }
})
