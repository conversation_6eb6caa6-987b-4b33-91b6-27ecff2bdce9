/* pages/faculty/faculty.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 师资统计样式 */
.stats-section {
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.stats-card {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 25rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  opacity: 0.9;
}

/* 搜索栏样式 */
.search-section {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  position: relative;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 50rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-icon, .clear-icon {
  position: absolute;
  right: 25rpx;
  font-size: 28rpx;
  color: #999;
}

.clear-icon {
  background: #ccc;
  color: white;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

/* 筛选栏样式 */
.filter-section {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.category-filter {
  white-space: nowrap;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.category-item.active {
  background: #1E88E5;
  color: white;
  border-color: #1E88E5;
}

/* 教师列表样式 */
.faculty-section {
  padding: 20rpx;
}

.faculty-count {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}

.faculty-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.faculty-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.faculty-card:active {
  transform: scale(0.98);
}

/* 教师头部信息 */
.faculty-header {
  display: flex;
  margin-bottom: 20rpx;
}

.faculty-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
  background: #f0f0f0;
}

.faculty-info {
  flex: 1;
}

.faculty-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.faculty-title {
  font-size: 26rpx;
  color: #1E88E5;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.faculty-college {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.faculty-speciality {
  font-size: 24rpx;
  color: #999;
}

.star-btn {
  padding: 10rpx;
}

.star-icon {
  font-size: 40rpx;
  color: #ddd;
  transition: color 0.3s;
}

.star-icon.starred {
  color: #FFD700;
}

/* 研究方向 */
.research-area {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.5;
}

.research-label {
  color: #666;
  font-weight: 500;
}

.research-text {
  color: #333;
}

/* 主要成就 */
.achievements {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.achievement-tag {
  background: #E3F2FD;
  color: #1E88E5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: 1rpx solid #BBDEFB;
}

/* 查看详情按钮 */
.view-detail-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 12rpx;
  color: white;
  font-size: 26rpx;
}

.arrow {
  font-size: 28rpx;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}
