<!--pages/faculty/faculty.wxml-->
<view class="container">
  <!-- 师资统计 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-title">师资力量</view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{facultyStats.total}}</view>
          <view class="stat-label">教职工总数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{facultyStats.professors}}</view>
          <view class="stat-label">教授</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{facultyStats.associates}}</view>
          <view class="stat-label">副教授</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{facultyStats.doctors}}</view>
          <view class="stat-label">博士学位</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{facultyStats.academicians}}</view>
          <view class="stat-label">院士</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-box">
      <input class="search-input" placeholder="搜索教师姓名、学院或研究方向" value="{{searchKeyword}}" bindinput="onSearchInput" />
      <view class="search-icon" wx:if="{{!searchKeyword}}">🔍</view>
      <view class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearSearch">✕</view>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="filter-section">
    <scroll-view class="category-filter" scroll-x="true">
      <view class="category-item {{selectedCategory === item.id ? 'active' : ''}}" 
            wx:for="{{categories}}" 
            wx:key="id" 
            bindtap="onCategoryChange" 
            data-category="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 教师列表 -->
  <view class="faculty-section">
    <view class="faculty-count">共找到 {{filteredFaculty.length}} 位教师</view>
    <view class="faculty-list">
      <view class="faculty-card" wx:for="{{filteredFaculty}}" wx:key="id" bindtap="viewFacultyDetail" data-faculty="{{item}}">
        <!-- 教师头像和基本信息 -->
        <view class="faculty-header">
          <image class="faculty-avatar" src="{{item.avatar}}" mode="aspectFill" />
          <view class="faculty-info">
            <view class="faculty-name">{{item.name}}</view>
            <view class="faculty-title">{{item.title}}</view>
            <view class="faculty-college">{{item.college}}</view>
            <view class="faculty-speciality">{{item.speciality}}</view>
          </view>
          <view class="star-btn" bindtap="toggleStar" data-index="{{index}}" catchtap="true">
            <text class="star-icon {{item.isStarred ? 'starred' : ''}}">{{item.isStarred ? '★' : '☆'}}</text>
          </view>
        </view>

        <!-- 研究方向 -->
        <view class="research-area">
          <text class="research-label">研究方向：</text>
          <text class="research-text">{{item.research}}</text>
        </view>

        <!-- 主要成就 -->
        <view class="achievements">
          <view class="achievement-tag" wx:for="{{item.achievements}}" wx:key="index" wx:for-item="achievement">
            {{achievement}}
          </view>
        </view>

        <!-- 查看详情按钮 -->
        <view class="view-detail-btn">
          <text>查看详情</text>
          <text class="arrow">→</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredFaculty.length === 0}}">
      <view class="empty-icon">👨‍🏫</view>
      <view class="empty-text">暂无相关教师</view>
      <view class="empty-tip">试试调整搜索条件或选择其他分类</view>
    </view>
  </view>
</view>
