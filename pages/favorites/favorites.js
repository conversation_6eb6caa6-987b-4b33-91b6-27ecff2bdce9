// pages/favorites/favorites.js
const { favoriteUtils, historyUtils, analyticsUtils } = require('../../utils/favorite-util')
const { shareUtils } = require('../../utils/share-util')

Page({
  data: {
    activeTab: 'majors',
    tabs: [
      { id: 'majors', name: '专业', icon: '📚' },
      { id: 'faculty', name: '教师', icon: '👨‍🏫' },
      { id: 'history', name: '历史', icon: '🕒' }
    ],
    favorites: {
      major: [],
      faculty: []
    },
    history: {
      major_view: [],
      faculty_view: [],
      search: []
    },
    stats: {
      favorites: { total: 0 },
      history: { total: 0 }
    },
    isEmpty: true
  },

  onLoad() {
    this.loadData()
    
    // 记录页面访问
    analyticsUtils.trackEvent('page_visit', {
      page: 'favorites',
      timestamp: new Date().toISOString()
    })
  },

  onShow() {
    this.loadData()
  },

  // 加载数据
  loadData() {
    const favorites = favoriteUtils.getFavorites()
    const history = historyUtils.getHistory()
    const favoriteStats = favoriteUtils.getFavoriteStats()
    const historyStats = historyUtils.getHistoryStats()

    this.setData({
      favorites: favorites,
      history: history,
      stats: {
        favorites: favoriteStats,
        history: historyStats
      }
    })

    this.checkEmpty()
  },

  // 检查是否为空
  checkEmpty() {
    const { activeTab, favorites, history } = this.data
    let isEmpty = true

    switch (activeTab) {
      case 'majors':
        isEmpty = !favorites.major || favorites.major.length === 0
        break
      case 'faculty':
        isEmpty = !favorites.faculty || favorites.faculty.length === 0
        break
      case 'history':
        isEmpty = (!history.major_view || history.major_view.length === 0) &&
                 (!history.faculty_view || history.faculty_view.length === 0) &&
                 (!history.search || history.search.length === 0)
        break
    }

    this.setData({ isEmpty })
  },

  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
    this.checkEmpty()

    // 记录标签切换
    analyticsUtils.trackEvent('tab_switch', {
      from: this.data.activeTab,
      to: tab,
      page: 'favorites'
    })
  },

  // 查看专业详情
  viewMajorDetail(e) {
    const { major } = e.currentTarget.dataset
    
    // 添加到历史记录
    historyUtils.addHistory(historyUtils.TYPES.MAJOR_VIEW, major)
    
    // 记录用户行为
    analyticsUtils.trackEvent('major_view', {
      majorId: major.id,
      majorName: major.name,
      source: 'favorites'
    })

    wx.navigateTo({
      url: `/pages/major-detail/major-detail?id=${major.id}`
    })
  },

  // 查看教师详情
  viewFacultyDetail(e) {
    const { faculty } = e.currentTarget.dataset
    
    // 添加到历史记录
    historyUtils.addHistory(historyUtils.TYPES.FACULTY_VIEW, faculty)
    
    // 记录用户行为
    analyticsUtils.trackEvent('faculty_view', {
      facultyId: faculty.id,
      facultyName: faculty.name,
      source: 'favorites'
    })

    wx.navigateTo({
      url: `/pages/faculty-detail/faculty-detail?id=${faculty.id}`
    })
  },

  // 移除收藏
  removeFavorite(e) {
    e.stopPropagation()
    const { type, item } = e.currentTarget.dataset

    wx.showModal({
      title: '确认取消收藏',
      content: `确定要取消收藏"${item.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          favoriteUtils.removeFavorite(type, item.id)
          this.loadData()
          
          // 记录用户行为
          analyticsUtils.trackEvent('remove_favorite', {
            type: type,
            itemId: item.id,
            itemName: item.name
          })
        }
      }
    })
  },

  // 移除历史记录
  removeHistory(e) {
    e.stopPropagation()
    const { type, item } = e.currentTarget.dataset

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          historyUtils.removeHistory(type, item.id)
          this.loadData()
          
          // 记录用户行为
          analyticsUtils.trackEvent('remove_history', {
            type: type,
            itemId: item.id
          })
        }
      }
    })
  },

  // 清空收藏
  clearFavorites() {
    const { activeTab } = this.data
    const typeMap = {
      'majors': 'major',
      'faculty': 'faculty'
    }
    const type = typeMap[activeTab]

    if (!type) return

    wx.showModal({
      title: '确认清空',
      content: `确定要清空所有${this.data.tabs.find(t => t.id === activeTab)?.name}收藏吗？`,
      success: (res) => {
        if (res.confirm) {
          favoriteUtils.clearFavorites(type)
          this.loadData()
          
          // 记录用户行为
          analyticsUtils.trackEvent('clear_favorites', {
            type: type,
            count: this.data.favorites[type]?.length || 0
          })
        }
      }
    })
  },

  // 清空历史记录
  clearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          historyUtils.clearHistory()
          this.loadData()
          
          // 记录用户行为
          analyticsUtils.trackEvent('clear_history', {
            totalCount: this.data.stats.history.total
          })
        }
      }
    })
  },

  // 重新搜索
  searchAgain(e) {
    const { item } = e.currentTarget.dataset
    const keyword = item.keyword

    // 记录搜索行为
    analyticsUtils.trackEvent('search_again', {
      keyword: keyword,
      source: 'history'
    })

    // 根据搜索内容跳转到相应页面
    wx.switchTab({
      url: '/pages/majors/majors',
      success: () => {
        // 通过事件通知搜索页面
        wx.setStorageSync('searchKeyword', keyword)
      }
    })
  },

  // 分享收藏
  shareFavorite(e) {
    e.stopPropagation()
    const { type, item } = e.currentTarget.dataset

    let shareConfig
    if (type === 'major') {
      shareConfig = shareUtils.getMajorShare(item)
    } else if (type === 'faculty') {
      shareConfig = shareUtils.getFacultyShare(item)
    }

    if (shareConfig) {
      wx.shareAppMessage({
        ...shareConfig,
        success: () => {
          shareUtils.onShareSuccess('favorite_share', type, shareConfig)
          analyticsUtils.trackEvent('share_favorite', {
            type: type,
            itemId: item.id,
            itemName: item.name
          })
        },
        fail: (error) => {
          shareUtils.onShareFail('favorite_share', type, error)
        }
      })
    }
  },

  // 分享功能
  onShareAppMessage() {
    const shareConfig = shareUtils.getPageShare('favorites', {
      title: '我的收藏 - 广东工业大学',
      desc: '查看我收藏的专业和教师信息'
    })

    analyticsUtils.trackEvent('page_share', {
      page: 'favorites',
      method: 'app_message'
    })

    return shareConfig
  },

  onShareTimeline() {
    const shareConfig = shareUtils.getPageShare('favorites', {
      title: '广东工业大学招生信息收藏'
    })

    analyticsUtils.trackEvent('page_share', {
      page: 'favorites',
      method: 'timeline'
    })

    return shareConfig
  }
})
