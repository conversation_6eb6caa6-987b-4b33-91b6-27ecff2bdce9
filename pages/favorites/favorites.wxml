<!--pages/favorites/favorites.wxml-->
<view class="container">
  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stat-item">
        <view class="stat-number">{{stats.favorites.total}}</view>
        <view class="stat-label">收藏总数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.history.total}}</view>
        <view class="stat-label">历史记录</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.favorites.major || 0}}</view>
        <view class="stat-label">收藏专业</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.favorites.faculty || 0}}</view>
        <view class="stat-label">收藏教师</view>
      </view>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tabs-section">
    <view class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
          wx:for="{{tabs}}" 
          wx:key="id" 
          bindtap="switchTab" 
          data-tab="{{item.id}}">
      <text class="tab-icon">{{item.icon}}</text>
      <text class="tab-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 收藏的专业 -->
  <view class="content-section" wx:if="{{activeTab === 'majors'}}">
    <view class="section-header" wx:if="{{!isEmpty}}">
      <view class="section-title">收藏的专业</view>
      <view class="clear-btn" bindtap="clearFavorites">清空</view>
    </view>
    
    <view class="majors-list" wx:if="{{!isEmpty}}">
      <view class="major-card" wx:for="{{favorites.major}}" wx:key="id" bindtap="viewMajorDetail" data-major="{{item}}">
        <view class="card-header">
          <view class="major-name">{{item.name}}</view>
          <view class="action-buttons">
            <view class="share-btn" bindtap="shareFavorite" data-type="major" data-item="{{item}}" catchtap="true">
              <text class="icon">📤</text>
            </view>
            <view class="remove-btn" bindtap="removeFavorite" data-type="major" data-item="{{item}}" catchtap="true">
              <text class="icon">❌</text>
            </view>
          </view>
        </view>
        
        <view class="major-info">
          <view class="info-item">
            <text class="label">学院：</text>
            <text class="value">{{item.collegeName}}</text>
          </view>
          <view class="info-item">
            <text class="label">就业率：</text>
            <text class="value employment">{{item.employment}}</text>
          </view>
          <view class="info-item">
            <text class="label">收藏时间：</text>
            <text class="value time">{{item.favoriteTime}}</text>
          </view>
        </view>
        
        <view class="major-tags">
          <text class="tag" wx:for="{{item.tags}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 收藏的教师 -->
  <view class="content-section" wx:if="{{activeTab === 'faculty'}}">
    <view class="section-header" wx:if="{{!isEmpty}}">
      <view class="section-title">收藏的教师</view>
      <view class="clear-btn" bindtap="clearFavorites">清空</view>
    </view>
    
    <view class="faculty-list" wx:if="{{!isEmpty}}">
      <view class="faculty-card" wx:for="{{favorites.faculty}}" wx:key="id" bindtap="viewFacultyDetail" data-faculty="{{item}}">
        <view class="card-header">
          <view class="faculty-name">{{item.name}}</view>
          <view class="action-buttons">
            <view class="share-btn" bindtap="shareFavorite" data-type="faculty" data-item="{{item}}" catchtap="true">
              <text class="icon">📤</text>
            </view>
            <view class="remove-btn" bindtap="removeFavorite" data-type="faculty" data-item="{{item}}" catchtap="true">
              <text class="icon">❌</text>
            </view>
          </view>
        </view>
        
        <view class="faculty-info">
          <view class="info-item">
            <text class="label">职称：</text>
            <text class="value">{{item.title}}</text>
          </view>
          <view class="info-item">
            <text class="label">学院：</text>
            <text class="value">{{item.college}}</text>
          </view>
          <view class="info-item">
            <text class="label">收藏时间：</text>
            <text class="value time">{{item.favoriteTime}}</text>
          </view>
        </view>
        
        <view class="research-area">
          <text class="label">研究方向：</text>
          <text class="content">{{item.research}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="content-section" wx:if="{{activeTab === 'history'}}">
    <view class="section-header" wx:if="{{!isEmpty}}">
      <view class="section-title">浏览历史</view>
      <view class="clear-btn" bindtap="clearHistory">清空</view>
    </view>
    
    <view class="history-list" wx:if="{{!isEmpty}}">
      <!-- 专业浏览历史 -->
      <view class="history-group" wx:if="{{history.major_view && history.major_view.length > 0}}">
        <view class="group-title">专业浏览记录</view>
        <view class="history-item" wx:for="{{history.major_view}}" wx:key="id" bindtap="viewMajorDetail" data-major="{{item}}">
          <view class="item-icon">📚</view>
          <view class="item-content">
            <view class="item-name">{{item.name}}</view>
            <view class="item-time">{{item.visitTime}}</view>
          </view>
          <view class="remove-btn" bindtap="removeHistory" data-type="major_view" data-item="{{item}}" catchtap="true">
            <text class="icon">✕</text>
          </view>
        </view>
      </view>

      <!-- 教师浏览历史 -->
      <view class="history-group" wx:if="{{history.faculty_view && history.faculty_view.length > 0}}">
        <view class="group-title">教师浏览记录</view>
        <view class="history-item" wx:for="{{history.faculty_view}}" wx:key="id" bindtap="viewFacultyDetail" data-faculty="{{item}}">
          <view class="item-icon">👨‍🏫</view>
          <view class="item-content">
            <view class="item-name">{{item.name}}</view>
            <view class="item-time">{{item.visitTime}}</view>
          </view>
          <view class="remove-btn" bindtap="removeHistory" data-type="faculty_view" data-item="{{item}}" catchtap="true">
            <text class="icon">✕</text>
          </view>
        </view>
      </view>

      <!-- 搜索历史 -->
      <view class="history-group" wx:if="{{history.search && history.search.length > 0}}">
        <view class="group-title">搜索记录</view>
        <view class="history-item" wx:for="{{history.search}}" wx:key="id" bindtap="searchAgain" data-item="{{item}}">
          <view class="item-icon">🔍</view>
          <view class="item-content">
            <view class="item-name">{{item.keyword}}</view>
            <view class="item-desc">找到 {{item.resultCount}} 个结果</view>
            <view class="item-time">{{item.visitTime}}</view>
          </view>
          <view class="remove-btn" bindtap="removeHistory" data-type="search" data-item="{{item}}" catchtap="true">
            <text class="icon">✕</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty}}">
    <view class="empty-icon">
      <text wx:if="{{activeTab === 'majors'}}">📚</text>
      <text wx:elif="{{activeTab === 'faculty'}}">👨‍🏫</text>
      <text wx:else>🕒</text>
    </view>
    <view class="empty-text">
      <text wx:if="{{activeTab === 'majors'}}">还没有收藏的专业</text>
      <text wx:elif="{{activeTab === 'faculty'}}">还没有收藏的教师</text>
      <text wx:else>暂无浏览历史</text>
    </view>
    <view class="empty-tip">
      <text wx:if="{{activeTab === 'majors'}}">去专业页面收藏感兴趣的专业吧</text>
      <text wx:elif="{{activeTab === 'faculty'}}">去师资页面收藏优秀的教师吧</text>
      <text wx:else>浏览专业和教师信息会自动记录</text>
    </view>
  </view>
</view>
