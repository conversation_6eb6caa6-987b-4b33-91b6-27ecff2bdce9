/* pages/favorites/favorites.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 统计信息 */
.stats-section {
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.stats-card {
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(30, 136, 229, 0.3);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 标签页导航 */
.tabs-section {
  background: white;
  display: flex;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  border-bottom-color: #1E88E5;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-name {
  font-size: 24rpx;
  color: #666;
}

.tab-item.active .tab-name {
  color: #1E88E5;
  font-weight: bold;
}

/* 内容区域 */
.content-section {
  padding: 0 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-btn {
  font-size: 26rpx;
  color: #FF6B6B;
  padding: 10rpx 20rpx;
  border: 1rpx solid #FF6B6B;
  border-radius: 20rpx;
}

/* 专业卡片 */
.majors-list, .faculty-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.major-card, .faculty-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.major-name, .faculty-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #1E88E5;
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
}

.share-btn, .remove-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.share-btn {
  background: #E3F2FD;
  color: #1E88E5;
}

.remove-btn {
  background: #FFEBEE;
  color: #F44336;
}

/* 信息项 */
.major-info, .faculty-info {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.label {
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
}

.value.employment {
  color: #4CAF50;
  font-weight: bold;
}

.value.time {
  color: #999;
  font-size: 22rpx;
}

/* 标签 */
.major-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.tag {
  background: #E3F2FD;
  color: #1E88E5;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
}

/* 研究方向 */
.research-area {
  font-size: 24rpx;
  line-height: 1.5;
}

.research-area .label {
  color: #666;
  margin-right: 10rpx;
}

.research-area .content {
  color: #333;
}

/* 历史记录 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.history-group {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.history-item:last-child {
  border-bottom: none;
}

.item-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}

.item-content {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.item-desc {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.item-time {
  font-size: 20rpx;
  color: #ccc;
}

.history-item .remove-btn {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #999;
  font-size: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}
