// pages/campus/campus.js
Page({
  data: {
    bannerImages: [
      '/images/campus/banner1.jpg',
      '/images/campus/banner2.jpg',
      '/images/campus/banner3.jpg'
    ],
    currentBanner: 0,
    schoolInfo: {
      name: '广东工业大学',
      englishName: 'Guangdong University of Technology',
      founded: '1958年',
      type: '公办本科',
      level: '省部共建高校',
      location: '广东省广州市',
      motto: '团结、勤奋、求是、创新'
    },
    campusData: [
      {
        id: 1,
        name: '大学城校区',
        area: '1700亩',
        address: '广州市番禺区大学城外环西路100号',
        features: ['主校区', '现代化建筑群', '完善的教学设施'],
        image: '/images/campus/daxuecheng.jpg'
      },
      {
        id: 2,
        name: '东风路校区',
        address: '广州市越秀区东风东路729号',
        area: '300亩',
        features: ['历史悠久', '市中心位置', '交通便利'],
        image: '/images/campus/dongfenglu.jpg'
      },
      {
        id: 3,
        name: '龙洞校区',
        address: '广州市天河区迎龙路161号',
        area: '200亩',
        features: ['环境优美', '专业特色', '实训基地'],
        image: '/images/campus/longdong.jpg'
      }
    ],
    historyData: [
      {
        year: '1958年',
        event: '广东工学院成立',
        description: '学校前身广东工学院正式成立，开启了工科教育的历程'
      },
      {
        year: '1995年',
        event: '更名为广东工业大学',
        description: '经国家教委批准，广东工学院更名为广东工业大学'
      },
      {
        year: '2004年',
        event: '迁入大学城校区',
        description: '主校区迁入广州大学城，办学条件得到极大改善'
      },
      {
        year: '2013年',
        event: '入选省部共建高校',
        description: '成为广东省人民政府和教育部共建高校'
      },
      {
        year: '2018年',
        event: '建校60周年',
        description: '学校迎来建校60周年，各项事业蓬勃发展'
      }
    ],
    facilitiesData: [
      {
        name: '图书馆',
        description: '现代化图书馆，藏书丰富，学习环境优雅',
        icon: '📚'
      },
      {
        name: '实验室',
        description: '先进的实验设备，支持各类科研和教学活动',
        icon: '🔬'
      },
      {
        name: '体育设施',
        description: '完善的体育场馆，包括游泳池、篮球场、足球场等',
        icon: '🏃‍♂️'
      },
      {
        name: '学生宿舍',
        description: '舒适的住宿环境，配备空调、热水等设施',
        icon: '🏠'
      },
      {
        name: '食堂餐厅',
        description: '多个食堂提供丰富美味的餐饮选择',
        icon: '🍽️'
      },
      {
        name: '创新创业基地',
        description: '支持学生创新创业，提供孵化和指导服务',
        icon: '💡'
      }
    ]
  },

  onLoad() {
    this.startBannerTimer()
  },

  onUnload() {
    this.clearBannerTimer()
  },

  // 轮播图自动切换
  startBannerTimer() {
    this.bannerTimer = setInterval(() => {
      const { currentBanner, bannerImages } = this.data
      const nextBanner = (currentBanner + 1) % bannerImages.length
      this.setData({
        currentBanner: nextBanner
      })
    }, 3000)
  },

  clearBannerTimer() {
    if (this.bannerTimer) {
      clearInterval(this.bannerTimer)
      this.bannerTimer = null
    }
  },

  // 轮播图切换
  onBannerChange(e) {
    this.setData({
      currentBanner: e.detail.current
    })
  },

  // 查看校区详情
  viewCampusDetail(e) {
    const { campus } = e.currentTarget.dataset
    wx.showModal({
      title: campus.name,
      content: `地址：${campus.address}\n面积：${campus.area}\n特色：${campus.features.join('、')}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查看历史详情
  viewHistoryDetail(e) {
    const { history } = e.currentTarget.dataset
    wx.showModal({
      title: history.year + ' - ' + history.event,
      content: history.description,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '广东工业大学 - 校园介绍',
      path: '/pages/campus/campus',
      imageUrl: '/images/campus/share.jpg'
    }
  },

  onShareTimeline() {
    return {
      title: '广东工业大学 - 美丽的校园等你来',
      imageUrl: '/images/campus/share.jpg'
    }
  }
})
