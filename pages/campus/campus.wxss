/* pages/campus/campus.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图样式 */
.banner-section {
  width: 100%;
  height: 400rpx;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 通用标题样式 */
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1E88E5;
  padding: 30rpx 30rpx 20rpx;
  background: linear-gradient(90deg, #1E88E5 0%, #42A5F5 100%);
  background: -webkit-linear-gradient(90deg, #1E88E5 0%, #42A5F5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 学校信息样式 */
.school-info-section {
  margin-bottom: 20rpx;
}

.school-info-card {
  background: white;
  margin: 0 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.school-name {
  text-align: center;
  margin-bottom: 30rpx;
}

.chinese-name {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 10rpx;
}

.english-name {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.school-motto {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #1E88E5, #42A5F5);
  border-radius: 12rpx;
  color: white;
}

.motto-label {
  font-size: 26rpx;
}

.motto-text {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
}

/* 校区介绍样式 */
.campus-section {
  margin-bottom: 20rpx;
}

.campus-list {
  padding: 0 20rpx;
}

.campus-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.campus-image {
  width: 100%;
  height: 300rpx;
}

.campus-info {
  padding: 30rpx;
}

.campus-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 15rpx;
}

.campus-area {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.campus-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.campus-features {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.feature-tag {
  background: #E3F2FD;
  color: #1E88E5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 历史沿革样式 */
.history-section {
  margin-bottom: 20rpx;
}

.timeline {
  padding: 0 20rpx;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: #1E88E5;
}

.timeline-item {
  position: relative;
  padding: 20rpx 0 20rpx 100rpx;
  margin-bottom: 20rpx;
}

.timeline-dot {
  position: absolute;
  left: -8rpx;
  top: 30rpx;
  width: 20rpx;
  height: 20rpx;
  background: #1E88E5;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 0 0 4rpx #1E88E5;
}

.timeline-content {
  background: white;
  padding: 25rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.timeline-year {
  font-size: 28rpx;
  font-weight: bold;
  color: #1E88E5;
  margin-bottom: 10rpx;
}

.timeline-event {
  font-size: 26rpx;
  color: #333;
}

/* 校园设施样式 */
.facilities-section {
  margin-bottom: 40rpx;
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 20rpx;
}

.facility-item {
  background: white;
  padding: 30rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.facility-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
}

.facility-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.facility-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}
