<!--pages/campus/campus.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500" bindchange="onBannerChange">
      <swiper-item wx:for="{{bannerImages}}" wx:key="index">
        <image class="banner-image" src="{{item}}" mode="aspectFill" />
      </swiper-item>
    </swiper>
  </view>

  <!-- 学校基本信息 -->
  <view class="school-info-section">
    <view class="section-title">学校概况</view>
    <view class="school-info-card">
      <view class="school-name">
        <text class="chinese-name">{{schoolInfo.name}}</text>
        <text class="english-name">{{schoolInfo.englishName}}</text>
      </view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">建校时间</text>
          <text class="info-value">{{schoolInfo.founded}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">办学性质</text>
          <text class="info-value">{{schoolInfo.type}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">办学层次</text>
          <text class="info-value">{{schoolInfo.level}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">学校位置</text>
          <text class="info-value">{{schoolInfo.location}}</text>
        </view>
      </view>
      <view class="school-motto">
        <text class="motto-label">校训：</text>
        <text class="motto-text">{{schoolInfo.motto}}</text>
      </view>
    </view>
  </view>

  <!-- 校区介绍 -->
  <view class="campus-section">
    <view class="section-title">校区分布</view>
    <view class="campus-list">
      <view class="campus-card" wx:for="{{campusData}}" wx:key="id" bindtap="viewCampusDetail" data-campus="{{item}}">
        <image class="campus-image" src="{{item.image}}" mode="aspectFill" />
        <view class="campus-info">
          <view class="campus-name">{{item.name}}</view>
          <view class="campus-area">占地面积：{{item.area}}</view>
          <view class="campus-address">{{item.address}}</view>
          <view class="campus-features">
            <text class="feature-tag" wx:for="{{item.features}}" wx:key="index" wx:for-item="feature">{{feature}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史沿革 -->
  <view class="history-section">
    <view class="section-title">历史沿革</view>
    <view class="timeline">
      <view class="timeline-item" wx:for="{{historyData}}" wx:key="year" bindtap="viewHistoryDetail" data-history="{{item}}">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-year">{{item.year}}</view>
          <view class="timeline-event">{{item.event}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 校园设施 -->
  <view class="facilities-section">
    <view class="section-title">校园设施</view>
    <view class="facilities-grid">
      <view class="facility-item" wx:for="{{facilitiesData}}" wx:key="name">
        <view class="facility-icon">{{item.icon}}</view>
        <view class="facility-name">{{item.name}}</view>
        <view class="facility-desc">{{item.description}}</view>
      </view>
    </view>
  </view>
</view>
