// 创建招生小程序图标的脚本
const sharp = require('sharp');
const fs = require('fs');

// 创建简单的SVG图标
const icons = {
  'campus': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <rect x="8" y="20" width="48" height="32" fill="#4CAF50" rx="4"/>
      <rect x="12" y="24" width="8" height="12" fill="#fff"/>
      <rect x="20" y="24" width="8" height="12" fill="#fff"/>
      <rect x="28" y="24" width="8" height="12" fill="#fff"/>
      <rect x="36" y="24" width="8" height="12" fill="#fff"/>
      <rect x="44" y="24" width="8" height="12" fill="#fff"/>
      <polygon points="32,8 8,20 56,20" fill="#2E7D32"/>
      <rect x="30" y="36" width="4" height="16" fill="#8BC34A"/>
    </svg>`,
    color: '#4CAF50'
  },
  'campus-active': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <rect x="8" y="20" width="48" height="32" fill="#2E7D32" rx="4"/>
      <rect x="12" y="24" width="8" height="12" fill="#fff"/>
      <rect x="20" y="24" width="8" height="12" fill="#fff"/>
      <rect x="28" y="24" width="8" height="12" fill="#fff"/>
      <rect x="36" y="24" width="8" height="12" fill="#fff"/>
      <rect x="44" y="24" width="8" height="12" fill="#fff"/>
      <polygon points="32,8 8,20 56,20" fill="#1B5E20"/>
      <rect x="30" y="36" width="4" height="16" fill="#4CAF50"/>
    </svg>`,
    color: '#2E7D32'
  },
  'major': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <rect x="12" y="8" width="40" height="48" fill="#2196F3" rx="4"/>
      <rect x="16" y="12" width="32" height="4" fill="#fff"/>
      <rect x="16" y="20" width="32" height="2" fill="#fff"/>
      <rect x="16" y="24" width="32" height="2" fill="#fff"/>
      <rect x="16" y="28" width="32" height="2" fill="#fff"/>
      <rect x="16" y="32" width="24" height="2" fill="#fff"/>
      <rect x="16" y="36" width="28" height="2" fill="#fff"/>
      <rect x="16" y="40" width="20" height="2" fill="#fff"/>
    </svg>`,
    color: '#2196F3'
  },
  'major-active': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <rect x="12" y="8" width="40" height="48" fill="#1976D2" rx="4"/>
      <rect x="16" y="12" width="32" height="4" fill="#fff"/>
      <rect x="16" y="20" width="32" height="2" fill="#fff"/>
      <rect x="16" y="24" width="32" height="2" fill="#fff"/>
      <rect x="16" y="28" width="32" height="2" fill="#fff"/>
      <rect x="16" y="32" width="24" height="2" fill="#fff"/>
      <rect x="16" y="36" width="28" height="2" fill="#fff"/>
      <rect x="16" y="40" width="20" height="2" fill="#fff"/>
    </svg>`,
    color: '#1976D2'
  },
  'faculty': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <circle cx="32" cy="20" r="12" fill="#FF9800"/>
      <path d="M32 32 C20 32, 12 40, 12 52 L52 52 C52 40, 44 32, 32 32 Z" fill="#FF9800"/>
      <rect x="28" y="8" width="8" height="4" fill="#FFC107" rx="2"/>
      <rect x="24" y="4" width="16" height="8" fill="#FFC107" rx="4"/>
    </svg>`,
    color: '#FF9800'
  },
  'faculty-active': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <circle cx="32" cy="20" r="12" fill="#F57C00"/>
      <path d="M32 32 C20 32, 12 40, 12 52 L52 52 C52 40, 44 32, 32 32 Z" fill="#F57C00"/>
      <rect x="28" y="8" width="8" height="4" fill="#FF9800" rx="2"/>
      <rect x="24" y="4" width="16" height="8" fill="#FF9800" rx="4"/>
    </svg>`,
    color: '#F57C00'
  },
  'admission': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <circle cx="32" cy="32" r="24" fill="#9C27B0"/>
      <rect x="28" y="16" width="8" height="16" fill="#fff" rx="4"/>
      <rect x="20" y="32" width="24" height="4" fill="#fff" rx="2"/>
      <polygon points="32,44 28,48 36,48" fill="#fff"/>
      <circle cx="32" cy="20" r="6" fill="#E1BEE7"/>
    </svg>`,
    color: '#9C27B0'
  },
  'admission-active': {
    svg: `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
      <circle cx="32" cy="32" r="24" fill="#7B1FA2"/>
      <rect x="28" y="16" width="8" height="16" fill="#fff" rx="4"/>
      <rect x="20" y="32" width="24" height="4" fill="#fff" rx="2"/>
      <polygon points="32,44 28,48 36,48" fill="#fff"/>
      <circle cx="32" cy="20" r="6" fill="#CE93D8"/>
    </svg>`,
    color: '#7B1FA2'
  }
};

// 创建图标文件
async function createIcons() {
  console.log('开始创建招生小程序图标...');
  
  for (const [name, config] of Object.entries(icons)) {
    try {
      // 创建PNG图标
      await sharp(Buffer.from(config.svg))
        .resize(64, 64)
        .png()
        .toFile(`images/${name}.png`);
      
      console.log(`✅ 创建图标: ${name}.png`);
    } catch (error) {
      console.error(`❌ 创建图标失败 ${name}:`, error.message);
    }
  }
  
  console.log('🎉 所有图标创建完成！');
}

// 运行脚本
if (require.main === module) {
  createIcons().catch(console.error);
}

module.exports = { createIcons };
