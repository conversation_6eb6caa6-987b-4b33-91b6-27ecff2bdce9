# 广东工业大学招生小程序 - 部署指南

## 🚀 项目概述

本项目是一个完整的微信小程序，专为广东工业大学招生宣传而开发。从原有的习惯追踪小程序完全重构，提供全面的招生信息服务。

## ✨ 核心功能

### 📱 主要页面
- **首页** - 学校介绍、快捷导航、特色展示、最新动态
- **专业介绍** - 专业列表、搜索筛选、详情查看、收藏功能
- **师资介绍** - 教师列表、分类筛选、个人详情、收藏功能
- **录取自查** - 录取查询、分数线、招生政策、招生日程
- **我的收藏** - 收藏管理、浏览历史、统计分析
- **招生咨询** - 智能问答、常见问题、人工客服
- **设置页面** - 联系方式、官网链接、意见反馈

### 🛠️ 技术特性
- **性能监控** - 页面加载时间、用户交互响应时间监控
- **错误处理** - 完善的错误捕获、分类和用户友好提示
- **缓存管理** - 智能缓存策略，提升用户体验
- **用户行为分析** - 详细的用户行为统计和分析
- **分享优化** - 完善的分享功能和SEO优化

## 📂 项目结构

```
├── pages/                      # 页面文件
│   ├── index/                  # 首页
│   ├── campus/                 # 校园介绍
│   ├── majors/                 # 专业介绍
│   ├── major-detail/           # 专业详情
│   ├── faculty/                # 师资介绍
│   ├── faculty-detail/         # 教师详情
│   ├── admission/              # 录取自查
│   ├── favorites/              # 我的收藏
│   ├── chat/                   # 招生咨询
│   ├── system-status/          # 系统状态（调试用）
│   └── settings/               # 设置页面
├── utils/                      # 工具函数
│   ├── util.js                # 通用工具
│   ├── admission-util.js       # 招生相关工具
│   ├── favorite-util.js        # 收藏和历史记录工具
│   ├── share-util.js           # 分享和SEO工具
│   └── performance-util.js     # 性能和错误处理工具
├── images/                     # 图片资源
├── app.js                     # 小程序入口
├── app.json                   # 小程序配置
├── app.wxss                   # 全局样式
├── README.md                  # 项目说明
└── DEPLOYMENT.md              # 部署指南
```

## 🔧 部署步骤

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 获取小程序AppID

### 2. 项目配置
1. 在微信开发者工具中导入项目
2. 修改 `app.json` 中的小程序配置
3. 替换项目中的AppID
4. 配置服务器域名（如需要）

### 3. 资源准备
1. 准备真实的学校图片资源
2. 更新 `utils/admission-util.js` 中的数据
3. 配置真实的联系方式和官网链接
4. 生成小程序码和分享图片

### 4. 数据配置

#### 专业数据配置
编辑 `utils/admission-util.js` 中的 `majorUtils.getDefaultMajors()`:
```javascript
{
  id: 1,
  name: "专业名称",
  college: "学院代码", 
  collegeName: "学院名称",
  description: "专业描述",
  employment: "就业率",
  salary: "薪资范围",
  tags: ["标签1", "标签2"]
}
```

#### 师资数据配置
编辑 `utils/admission-util.js` 中的 `facultyUtils.getDefaultFaculty()`:
```javascript
{
  id: 1,
  name: "教师姓名",
  title: "职称",
  college: "所属学院",
  speciality: "专业领域", 
  research: "研究方向",
  achievements: ["成就1", "成就2"]
}
```

#### 学校信息配置
编辑 `utils/admission-util.js` 中的 `campusUtils.getCampusInfo()`:
```javascript
{
  name: "学校名称",
  founded: "建校时间",
  motto: "校训",
  campuses: [校区信息]
}
```

### 5. 测试验证
1. 运行测试脚本：`node test-miniprogram.js`
2. 在开发者工具中预览
3. 真机调试测试
4. 功能完整性检查

### 6. 发布上线
1. 点击"上传"按钮
2. 填写版本号和更新说明
3. 登录微信公众平台
4. 提交审核
5. 审核通过后发布

## 📊 监控和维护

### 性能监控
- 使用 `performanceUtils` 监控页面加载性能
- 查看 `/pages/system-status/system-status` 页面获取详细报告
- 定期清理缓存和日志

### 错误监控
- 所有错误会自动记录到本地存储
- 可通过系统状态页面查看错误统计
- 建议定期导出错误日志进行分析

### 用户行为分析
- 用户操作会自动记录
- 可查看收藏、搜索、分享等行为统计
- 用于优化用户体验和功能设计

## 🔒 安全注意事项

1. **数据安全**
   - 不要在代码中硬编码敏感信息
   - 定期清理用户数据和日志
   - 遵守数据保护法规

2. **权限管理**
   - 合理申请小程序权限
   - 用户隐私保护
   - 数据使用透明化

3. **内容审核**
   - 确保所有内容符合平台规范
   - 定期更新招生信息
   - 避免虚假宣传

## 📈 优化建议

### 性能优化
1. **图片优化**
   - 使用WebP格式
   - 压缩图片大小
   - 实现懒加载

2. **代码优化**
   - 减少不必要的数据传递
   - 优化页面渲染逻辑
   - 使用分包加载

3. **缓存策略**
   - 合理设置缓存时间
   - 定期清理过期缓存
   - 优化数据更新机制

### 用户体验优化
1. **交互优化**
   - 添加加载动画
   - 优化页面切换效果
   - 提供操作反馈

2. **内容优化**
   - 定期更新招生信息
   - 优化搜索算法
   - 完善问答库

3. **功能扩展**
   - 添加推送通知
   - 集成地图导航
   - 支持在线报名

## 🆘 常见问题

### Q: 如何更新招生数据？
A: 编辑 `utils/admission-util.js` 文件中的相应数据结构，重新上传小程序。

### Q: 如何添加新的专业或教师？
A: 在对应的数据数组中添加新的对象，确保ID唯一。

### Q: 如何查看用户使用统计？
A: 访问 `/pages/system-status/system-status` 页面查看详细统计。

### Q: 如何处理用户反馈？
A: 通过设置页面的联系方式收集反馈，定期查看错误日志。

### Q: 如何备份用户数据？
A: 使用系统状态页面的导出功能，定期备份重要数据。

## 📞 技术支持

如有技术问题，请联系：
- 开发团队邮箱：<EMAIL>
- 技术支持电话：020-39322681
- 项目文档：查看 README.md

---

**广东工业大学招生小程序** - 让招生信息查询更便捷！🎓
